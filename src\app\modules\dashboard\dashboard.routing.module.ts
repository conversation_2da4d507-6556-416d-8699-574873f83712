import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { MainLayoutComponent } from 'src/app/modules/shared-modules/layouts/main-layout/main-layout.component';
import { EpividianGuardService } from 'src/app/shared-services/epividian-guard.service';
import { DashboardComponent } from './dashboard/dashboard.component';
import { ReportComponent } from './report/report.component';
import { HuddleComponent } from './huddle/huddle.component';
import { ReportRedirectComponent } from './report-redirect/report-redirect.component';
import { MasterReportComponent } from './panels/master-report/master-report.component';
import { MobileDeviceManagementComponent } from '../shared-modules/layouts/dialogs/MobileDeviceManagement/MobileDeviceManagement.component';
import { AnnotationComponent } from '../annotation/annotation.component';
import { RegimenBuilderComponent } from './panels/pnl-custom-query/regimen-builder/regimen-builder.component';
import { CustomQueryComponent } from './customQuery/customQuery.component';
import { HelpScreenComponent } from '../shared-modules/layouts/help-screen/help-screen.component';
import { EHIExportComponent as EHIExportComponent } from './ehi-export/ehi-export.component';
import { QrdaExportComponent } from './qrda-export/qrda-export.component';
import { DynamicFormsComponent } from './dynamic-forms/dynamic-forms.component';
import { PdfFillerComponent } from './pdf-filler/pdf-filler.component';
import { QualityMeasuresComponent } from './quality-measures/quality-measures.component';

const routes: Routes = [
  {
    path: 'Dashboard',
    component: MainLayoutComponent,
    canActivate: [EpividianGuardService],
    children: [
      { path: "", component: DashboardComponent, pathMatch: "full"},
      { path: "Report/:siteId/:reportFileName", component: ReportComponent, pathMatch: "full" },
      { path: "ReportRedirect/:siteId/:reportFileName", component: ReportRedirectComponent, pathMatch: "full" },
      { path: "masterreport", component: MasterReportComponent, pathMatch: "full" },
      { path: "regimenBuilder", component: RegimenBuilderComponent, pathMatch: "full" },
      { path: "Report/:siteId", component: CustomQueryComponent, pathMatch: "full" },
      { path: "Report/:siteId/CustomQuery/Criteria", component: CustomQueryComponent, pathMatch: "full" },
      { path: "Forms/:siteId/:workFlowId", component: DynamicFormsComponent, pathMatch: "full" },
      { path: "pdffiller", component: PdfFillerComponent, pathMatch: "full" },
      { path: "EHI_Export/:siteId", component: EHIExportComponent, pathMatch: "full" },
      { path: "QRDA_Export/:siteId", component: QrdaExportComponent, pathMatch: "full" },
      { path: "QualityMeasures/:siteId", component: QualityMeasuresComponent, pathMatch: "full" },
      { path: "huddle", component: HuddleComponent, pathMatch: "full" }
    ]
  },
  { path: "DeviceManagement", component: MobileDeviceManagementComponent, pathMatch: "full" },
  { path: "annotation", component: AnnotationComponent, pathMatch: "full" },
  { path: "HelpScreen/:siteId", component: HelpScreenComponent, pathMatch: "full"}

]

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class DashboardRoutingModule { }

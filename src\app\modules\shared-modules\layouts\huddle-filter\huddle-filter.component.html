<div class="filter-container" >
    <h3 class="filter-title">Filter</h3>
    <div *ngFor="let filter of filters; let i = index"  class="filter-item" [class.active]="i == selectedIndex" (click)="selectFilter(i)">
      <span class="filter-count" [class.active-count]="i == selectedIndex">{{ getAppointmentsWithFlagSet(filter.flagBit) }}</span>
      <span class="filter-text">{{ filter.name }}</span>
      <div *ngIf="i > 0" class="filterToolTip" [title]="getFilterToolTip(filter.name)">i</div> 
    </div>
  </div>


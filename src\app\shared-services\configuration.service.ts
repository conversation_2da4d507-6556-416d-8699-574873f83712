import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, lastValueFrom } from 'rxjs';
import { pipelineUpdates } from './appversion';

export enum EnvironmentType {
    Development = "Development",
    Test = "Test",
    Production = "Production",
    Local = "Local"
  }

export const ENVIRONMENTS = {
[EnvironmentType.Development]: {
    production: false,
    mockData: false,
    apiV1: "https://api-dev.epividian.com/V1",
    apiV2: "https://api-dev.epividian.com/V2",
    authProvider: "https://authprovider-dev.epividian.com",
    reportingUrl: "",
    pdfFiller: "https://pdf-dev.epividian.com",
    encodeBrowserObjects: false,
    showBoldReportParameters: false,
    sessionTimeout: 0,
    countDownOffsetMinutes: 1,
    env: "Development",
    appVersion: pipelineUpdates.appVersion,
    AppStore:"https://itunes.apple.com/us/app/epividian-chorus/id1302007494?mt=8",
    PlayStore:"https://play.google.com/store/apps/details?id=com.epividian.chorus",
    ShowSupportLink: false
  },
[EnvironmentType.Test]: {
    production: true,
    mockData: false,
    apiV1: "https://api-test.epividian.com/V1",
    apiV2: "https://api-test.epividian.com/V2",
    authProvider: "https://authprovider-test.epividian.com",
    reportingUrl: "",
    pdfFiller: "https://pdf-test.epividian.com",
    encodeBrowserObjects: false,
    showBoldReportParameters: false,
    sessionTimeout: 0,
    countDownOffsetMinutes: 1,
    env: "Test",
    appVersion: pipelineUpdates.appVersion,
    AppStore:"https://itunes.apple.com/us/app/epividian-chorus/id1302007494?mt=8",
    PlayStore:"https://play.google.com/store/apps/details?id=com.epividian.chorus",
    ShowSupportLink: false
  },
[EnvironmentType.Production]: {
    production: true,
    mockData: false,
    apiV1: "https://api-prod.epividian.com/Mobile",
    apiV2: "https://api-prod.epividian.com/Web",
    authProvider: "https://authprovider.epividian.com",
    reportingUrl: "",
    pdfFiller: "https://pdf.epividian.com",
    encodeBrowserObjects: false,
    showBoldReportParameters: false,
    env: "Production",
    sessionTimeout: 0,
    countDownOffsetMinutes: 1,
    appVersion: pipelineUpdates.appVersion,
    AppStore:"https://itunes.apple.com/us/app/epividian-chorus/id1302007494?mt=8",
    PlayStore:"https://play.google.com/store/apps/details?id=com.epividian.chorus",
    ShowSupportLink: false
},
[EnvironmentType.Local]: {
  production: false,
  mockData: false,
  apiV1: "https://api-dev.epividian.com/V1",
  apiV2: "http://localhost:38734",
  authProvider: "https://authprovider-dev.epividian.com",
  reportingUrl: "",
  //pdfFiller: "http://localhost:4300",
  pdfFiller: "https://pdf-dev.epividian.com",
  encodeBrowserObjects: false,
  showBoldReportParameters: false,
  sessionTimeout: 8,
  countDownOffsetMinutes: 1,
  env: "Local",
  appVersion: pipelineUpdates.appVersion,
  AppStore:"https://itunes.apple.com/us/app/epividian-chorus/id1302007494?mt=8",
  PlayStore:"https://play.google.com/store/apps/details?id=com.epividian.chorus",
  ShowSupportLink: false
},
};

// src/app/models/config.model.ts
export interface AppEnv {
    environment: EnvironmentType;
  }

@Injectable({
  providedIn: 'root'
})
export class ConfigurationService {
  public env: AppEnv = { environment: EnvironmentType.Development };

  constructor(private http: HttpClient) { }

  public getConfig(): Promise<AppEnv> {
    return this.loadConfig();
  }



  public async loadConfig(): Promise<AppEnv> {
    try {
    return await lastValueFrom(this.http.get('/assets/configs/config.json') as Observable<AppEnv>,
                  { defaultValue: { environment: EnvironmentType.Development } });
    } catch (error) {
      console.log('Error loading config defaulting to development env');
      return Promise.resolve({ environment: EnvironmentType.Development })
    }
  }


  public isValidEnvironmentType(value: any): boolean {
    return Object.values(EnvironmentType).includes(value);
  }


  public returnEnvVariables(env: EnvironmentType): any {
    return ENVIRONMENTS[env];
  }

  setConfig(env: AppEnv): void {
    this.env = env;
  }

}

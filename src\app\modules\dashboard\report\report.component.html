<!--Report Dashboard with Left Stacked Panels-->
<div class="report-wrapper">
  <!-- Report Header -->
  <div class="report-header">
    <div class="header-left">
      <h2 class="report-title">{{reportTitle}}</h2>
    </div>
    <div class="header-right">
      <button
        type="button"
        mat-icon-button
        *ngIf="isPopUp"
        (click)="closeRptPop()"
        aria-label="Close report popup"
        title="Close">
        <mat-icon>close</mat-icon>
      </button>
    </div>
  </div>

  <!-- Report Content Layout -->
  <div class="report-content-layout">
    <!-- Left Sidebar: Stacked Panels -->
    <div class="left-sidebar">
      <!-- Reports Panel -->
      <div class="sidebar-panel reports-panel"
           [class.collapsed]="reportNavCollapsed"
           [class.expanded]="!reportNavCollapsed && filtersCollapsed">
        <div class="panel-header">
          <h3>Select Report</h3>
          <button
            type="button"
            mat-icon-button
            class="panel-toggle-btn"
            (click)="toggleReportNav()"
            [attr.aria-label]="reportNavCollapsed ? 'Show reports' : 'Hide reports'"
            title="Toggle Reports">
            <mat-icon>{{ reportNavCollapsed ? 'expand_more' : 'expand_less' }}</mat-icon>
          </button>
        </div>
        <div class="panel-content" [class.hidden]="reportNavCollapsed">
          <epividian-reportNav></epividian-reportNav>
        </div>
      </div>

      <!-- Filters Panel -->
      <div class="sidebar-panel filters-panel"
           [class.collapsed]="filtersCollapsed"
           [class.expanded]="!filtersCollapsed && reportNavCollapsed">
        <div class="panel-header">
          <h3>Select Filters</h3>
          <button
            type="button"
            mat-icon-button
            class="panel-toggle-btn"
            (click)="toggleFilters()"
            [attr.aria-label]="filtersCollapsed ? 'Show filters' : 'Hide filters'"
            title="Toggle Filters">
            <mat-icon>{{ filtersCollapsed ? 'expand_more' : 'expand_less' }}</mat-icon>
          </button>
        </div>
        <div class="panel-content" [class.hidden]="filtersCollapsed">
          <div class="filters-container">
            <ng-template #panelContainerRef></ng-template>
          </div>
        </div>
      </div>
    </div>

    <!-- Right: Report Viewer -->
    <div class="report-viewer-area">
      <ng-template #bldReportContainerRef></ng-template>
    </div>
  </div>
</div>


.quality-measures-container {
  background-color: #efefef;
}

h1 {
  font-family: "MuseoSans-500","sans-serif";
  font-size: 24px;
  margin-left: 10px;
  margin-bottom: 3px;
  color: #0071bc;
}

.header {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
  margin-left: 10px;
  margin-right: 10px;
}

.header h2 {
  font-family: "MuseoSans-500","sans-serif";
  text-align: center;
  font-size: 18px;
  margin-bottom: 3px;
  color: #0071bc;
}

.header p {
  font-size: 20px;
  color: #666;
  margin-bottom: 10px;
}

.header a {
  color: #0077cc;
  text-decoration: none;
}

.header div {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.measures-table {
  width: 100%;
  border-collapse: collapse;
  overflow: hidden;
}

.table-notes {
  border-radius: 12px;
  background-color: #f9f9f9;
  overflow: hidden;
}

.filled-satisfied-icon {
  color: #99d2f8;
  font-size: 10px;
  text-align: right;
  vertical-align: middle !important;
  padding-top: 5px;
  padding-right: 3px;
}
.filled-unsatisfied-icon {
  color: #0089e3;
  font-size: 10px;
  text-align: right;
  vertical-align: middle !important;
  padding-top: 5px;
  padding-right: 3px;
}

.data-notes {
  border-top: 2px solid #d8d8d8;
  border-left: 2px solid #cccccc;
  border-right: 2px solid #cccccc;
  border-bottom: 2px solid #cccccc;
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
  background-color: #f2f2f2;
  color: #666666;
  padding: 10px;
}

.number {
  text-align: right;
  padding-right: 20px;
}

/* Drop down menu for PDF and Excel export */
.dropdown {
  position: relative;
  display: inline-block;
}

.menu {
  position: absolute;
  border-radius: 6px;
  border: 2px solid #b7b7b7;
  background-color: #efefef;
  z-index: 1;
  min-width: 180px;
  right: 0;
  top: 100%;
  /* Optionally add transition/animation here */
}

.menu>ul {
  list-style: none;
  margin: .4rem 0;
}
.menu>ul>li {
  padding: 0;
}
.menu>ul>li>button {
  /* padding: .2rem .8rem; */
  padding: 5px;
  padding-left: 20px;
  width: 100%;
  min-width: 150px;
  font-size: 1rem;
  text-transform: none;
  font-family: inherit;
  font-size: 100%;
  line-height: 1.15;
  margin: 0;
  overflow: visible;
  border: 0;
  text-align: left;
  outline: 0;
  cursor: pointer;
  background: 0 0;
}

nav ul {
  padding-left: 0;
}

/* fix for firefox background color */
.downloadButton {
  background-color: #eff1f6;
  border: none;
  cursor: pointer;
  text-decoration: underline;
}

/* For debugging */
/* * {
  border: 0.1px solid red !important;
} */
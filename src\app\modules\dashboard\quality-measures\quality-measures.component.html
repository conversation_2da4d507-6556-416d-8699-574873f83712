<div class="container-fluid mx-0 px-0">
  <div class="row g-0 mx-0">
    <div class="col-md-3">
      <div class="container px-0" style="height: 300px;">
        <h1>Reports</h1>
        <p>New Reports Menu goes here</p>
      </div>
      <div class="container px-0">
        <app-quality-measure-filters (filtersChanged)="onFiltersChanged($event)"></app-quality-measure-filters>
      </div>
    </div>
    <div class="col-md-9">
      <div class="quality-measures-container">
        <div class="header">
          <h2>Quality Measures</h2>
          <div style="position: relative;">
            <span>{{ getTotalMeasuresSum() | number }} Qualifying Patients: COMES FROM FILTER, Measure Year FILTER</span>
            <span style="position: absolute; right: 0;">
              <div class="dropdown" (document:click)="onClickOutsideMenu($event)">
                <button class="downloadButton" (click)="toggleMenu(); $event.stopPropagation();">
                  <mat-icon class="material-symbols-outlined" style="font-size: 16px; float: left;">ios_share</mat-icon>Download or Print
                </button>
                <nav class="menu" *ngIf="menuOpen">
                  <ul>
                    <li>
                      <div class="row g-0">
                        <div class="col-11" style="align-items: start !important; justify-content: left !important;">
                          <mat-icon class="material-symbols-outlined" style="font-size: 14px;">ios_share</mat-icon>
                          <span style="font-size: 11px; font-style: italic; color: #b7b7b7;" class="fw-lighter">Export&nbsp;Report</span>
                        </div>
                        <div class="col-1" style="cursor: pointer;" (click)="toggleMenu()">
                          <mat-icon class="material-symbols-outlined" style="font-size: 12px; color: #b7b7b7;">close</mat-icon>
                        </div>
                      </div>
                    </li>
                    <li style="border: 1px solid #dfdfdf;"><button (click)="printTable();">Print</button></li>
                    <li><button (click)="downloadPDF();">PDF Export</button></li>
                    <li><button (click)="downloadExcel();">Excel Export</button></li>
                    <li><button (click)="downloadCSV();">CSV Export</button></li>
                  </ul>
                </nav>
              </div>
            </span>
          </div>
        </div>
        <div class="table-notes">
            <div *ngIf="qualityMeasures.length > 0">
              <div id="quality-measures-table"></div>
            </div>
            <div *ngIf="qualityMeasures.length === 0">
              No quality measures available.
            </div>
            <div class="data-notes">
              <h3>Data Notes</h3>

              <ul>
                <li>The Merit-based Incentive Payment System (MIPS) is a CMS quality reporting program for covered Physician Fee
                  Schedule services furnished to Medicare Part B Fee-For-Service beneficiaries. All 2022 MIPS &amp; eCQM
                  measures can be found at: <a href="https://qpp.cms.gov/">https://qpp.cms.gov/</a></li>
                <li>The All OPERA Average comparison data is only available to sites participating in OPERA</li>
                <li>For patients with a recent HIV diagnosis (last 6 months) or are new to the practice (first visit was within
                  the last 6 months), the Engaged Patient measure is adjusted to allow for only one visit in the last year.</li>
                <li>Patients who are deceased are included in these measures.</li>
                <li>PCP prophylaxis: CD4 absolute &lt;= 200 and not on Bactrim or Atovaquone or Pentamidine</li>
                <li>Toxoplasma prophylaxis: CD4 absolute &lt;= 100 and not on Bactrim or (Dapsone plus Pyrimethamine plus
                  Leucovorin) or Atovaquone</li>
                <li>Mycobacterium avium complex prophylaxis (MAC): CD4 absolute &lt;=50 and not on Azithromycin or
                  Clarithromycin or Rifabutin</li>
                <li>Custom KPIs designed and developed to meet your enterprise specific health performance targets and
                  objectives.</li>
                <li>VAX measures are based on the CDC recommended adult immunization schedule for ages 19 years or older and can
                  be found at: <a
                    href="https://www.cdc.gov/vaccines/schedules/downloads/adult/adult-combined-schedule.pdf">https://www.cdc.gov/vaccines/schedules/downloads/adult/adult-combined-schedule.pdf</a>
                </li>
              </ul>

              <p><span style="font-weight: bold; font-size: large;">OPERA &reg; Score</span> - A realtime calculation based on a linear percentage distribution of the total OPERA
              population (excluding the current site data) in relation to the data measurement being compared.</p>
              <ul>
                <li class="score-item">&darr;25% - Represents a measure that falls in the bottom 25% of the Active OPERA
                  population.</li>
                <li class="score-item">Median - Represents a measure that lies between 25% and 75% of the Active OPERA
                  population.</li>
                <li class="score-item">&uarr;25% - Represents a measure that is in the top 25% of the Active OPERA population
                </li>
                <li class="score-item">Leader - A measure that exceeds all of the current Active OPERA population.</li>
                <li>The OPERA Score comparison data is only available to sites participating in OPERA</li>
              </ul>

            </div>
        </div>
      </div>

    </div>
  </div>
</div>

import { environment } from "src/environments/environment";

export enum ApiRoutes {
  //LoginFlow and AuthProvider
  choruslogin = "/ChorusLogin",
  AuthProviderLoginToken = "/api/authorize/token",
  ResetPassword = "/api/authorize/resetPassword?ref={{resetToken}}",
  ForgotPassword = "/api/authorize/forgotPassword",
  ChangePassword = "/api/manage/ChangePassword",
  RefreshToken = "/api/authorize/refresh-token/{{refreshToken}}",
  AddTfaPhone =  "/api/authorize/AddTfaPhone/{{userId}}/{{refreshToken}}",
  SendTFASMS = "/api/TFA/SendTFASMS",
  VerifyTFACode = "/api/authorize/VerifyTFACode/{{userId}}/{{refreshToken}}/{{tfaToken}}",
  VerifyTOTP = "/api/authorize/VerifyTOTP/{{userId}}/{{refreshToken}}/{{tfaToken}}",
  ResendCode = "/api/authorize/ResendCode/{{userId}}/{{refreshToken}}",
  QRCode = "/api/authorize/QRCode/{{userName}}/{{refreshToken}}",
  Logout = "/api/authorize/Logout/{{fromInactive}}",

  //Device Management
  DeviceList = "/api/User/DeviceList",
  UpdateDeviceStatus = "/api/User/UpdateDeviceStatus/{{DeviceId}}/{{isActive}}",
  DeleteDevice = "/api/User/DeleteDevice/{{DeviceId}}",
  ManageDevicePinInfo = "/api/user",
  SendAppLink = "/api/Services/SendAppLink/{{MethodId}}/{{DeviceTypeId}}",

  //Initial Data for the User
  Report = '/api/Report',
  ReportwithFilter = '/api/Report?siteId={{siteId}}&showpanel={{showpanel}}&rpt={{reportName}}&params={{params}}',
  getUsersSites = '/api/user/GetSites',
  getUserAccess = '/api/AuthProvider/GetUserAccess',
  UpdateUserLoginStatus='/api/User/UpdateLoginStatus',

  //Terms Agreement and Content
  ChorusUserInfo = '/api/User/ChorusUserInfo',
  Eulacontent = '/api/User/eula/content',
  SaveEulaStatus = '/api/User/eula/',

  // Retention
  getOutreachCallDetails = '/api/Retention/outreachcalls/',
  getPatientMeasureDetail = '/api/Retention/PatientMeasureDetails/',
  getFullOutreachHistory = '/api/Retention/FullOutreachHistory/',
  getAdminActionDetail = '/api/Retention/AdminActionDetails/',
  getRetentionData = '/api/Retention/RetentionData/',
  getProviderViewData = '/api/Retention/Provider/',
  SaveProviderAnnotationStatus = '/api/Retention/SaveProviderAnnotationStatus/',
  getRecordResultOptions='/api/Retention/GetRecordResultOptions/',
  UpdateRetentionResult='/api/Retention/UpdateRetentionResult/',

  //report viewer and parameter data
  reportViewer = '/api/ReportViewer/{{siteid}}',
  GetSSRSParams =  '/api/ReportViewerSSRS/GetParams/{{rptName}}',
  ReportParamData='/api/panel/ReportParamData/{{siteid}}/{{panelid}}',
  GetAutoCompleteData='/api/panel/PatientNames/{{siteid}}/{{searchtype}}/{{searchtext}}',
  GetDateofLastUpdate= '/api/panel/DateLastUpdate/{{siteid}}',
  PatientFlowsheetReport = '/Dashboard/Report/{{siteId}}/PatientFlowsheet?DEMOGRAPHICS_ID={{demographicId}}',
  QualityGapReport = '/Dashboard/Report/{{siteId}}/Patient_Quality_Measures_DTL?DEMOGRAPHICS_ID={{demographicId}}',

  //Qrda transform
  getQRDA = '/api/QRDA/GetQRDA/{{siteId}}/{{locationId}}/{{year}}/{{reportFormat}}/{{patientMRN}}',

  //Custom Query Report Data
  CustomQueryData = '/api/CustomQuery/{{siteid}}/Criteria',

  //Schedule Appointments
  GetProviders='/api/Appointment/GetProviders',
  GetAppointments='/api/Appointment/GetAppointments',
  GetFormUrls = '/api/Appointment/GetFormUrls/{siteId}',

  //Annotation
  GetAnnotationDetails='/V1/api/Annotation/Details/',
  SaveMeasureResponse='/V1/api/Annotation/Save/',

  // Regimen Builder
  GetMedicationsAndGroups = '/api/CustomQuery/{{siteid}}/GetMedicationsAndGroups',
  GetCustomQuery = '/api/CustomQuery/{{siteid}}/GetCustomQuery',

  //Documents
  DocumentGetList = '/api/DocumentStore/{{siteId}}/GetDirectoryList/{{path}}',
  DocumentGetListSorted = '/api/DocumentStore/{{siteId}}/GetDirectoryList/{{path}}/sortColumn/{{sortColumn}}/ascend/{{ascend}}',
  DocumentRename = '/api/DocumentStore/{{siteId}}/Rename',
  DocumentUpload = '/api/DocumentStore/{{siteId}}/Upload/{{FileNamePath}}',
  DocumentDelete = '/api/DocumentStore/{{siteId}}/Delete/{{FileNamePath}}',
  DocumentDownload = '/api/DocumentStore/{{siteId}}/Download/{{FileNamePath}}',
  DocumentCreate = '/api/DocumentStore/{{siteId}}/CreateDir/{{dirName}}',
  DocumentMove = '/api/DocumentStore/{{siteId}}/Move/{{FileNamePath}}',
  DownloadFile = '/Shared%20Documents/{{FileNamePath}}',

  //Help
  GetHelpIds = '/api/Help/{{siteId}}',
  GetHelpContent = '/api/Help/{{siteId}}/GetHelpContent',

  //Export
  GetEHIExport = '/api/Export/Patient/EHI/{{siteId}}/{{demographicId}}/{{exportId}}',
  GetEHIExportPassword = '/api/Export/File/Security/{{exportId}}',

  //Rules
  ExecuteWorkFlow = '/api/RulesSite/{{siteId}}/ExecuteWorkFlow/{{workflowId}}?guid={{workflowGuid}}',
  GetWorkflowStatuses = '/api/RulesSite/{{siteId}}/WorkFlowRuleResult/{{workflowId}}/{{ruleId}}',

  // Rules Engine
  GetRules = '/api/Rules',
  GetRuleById = '/api/Rules/{{ruleId}}',
  GetRuleBySite = '/api/RulesSite/{{siteId}}/{{ruleId}}',
  GetWorkflows = '/api/Rules/workflows',
  GetWorkflowById = '/api/Rules/workflows/{{workflowId}}',
  GetWorkflowRules = '/api/Rules/workflows/{{workflowId}}/rules',
  AssignRuleToWorkflow = '/api/Rules/workflows/{{workflowId}}/rules/{{ruleId}}',
  UpdateRuleOrder = '/api/Rules/workflows/{{workflowId}}/rules/order',
  WorkflowStatus = '/api/RulesSite/{{siteId}}/WorkFlowStatus/{{workflowId}}/{{guid}}',
  WorkflowRuleResult = '/api/RulesSite/{{siteId}}/WorkFlowRuleResult/{{workflowId}}/{{ruleId}}',
  WorkflowFileResult = '/api/RulesSite/{{siteId}}/WorkFlowFileResult/{{workflowId}}/{{ruleId}}',
  WorkflowProcessIds = '/api/RulesSite/{{siteId}}/WorkFlowProcessIds',
  RuleDefinitionByType = '/api/Rules/RuleDefinitionbyType/{{ruleType}}',
  RuleDataByName = '/api/RulesSite/{{siteId}}/Data/{{ruleName}}',

  //PdfFiller
  PdfFillerViewer = '/{{siteId}}/{{formRuleId}}/{{workFlowProccessId}}',

  //huddle
  HuddleGetProviders = '/api/Huddle/GetProviders/{{siteId}}/{{date}}',
  HuddleGetLocations = '/api/Huddle/GetLocations/{{siteId}}',
  HuddleGetDetails = '/api/Huddle/GetHuddleDetails/{{siteId}}/{{date}}/{{filter}}',
  HuddleGetFilters = '/api/Huddle/GetFilters/{{siteId}}',

  //Feature Access
  FeatureAccessWithSite = '/api/User/FeatureAccess/{{siteId}}/{{featureAccess}}',
  FeatureAccess = '/api/User/FeatureAccess/{{featureAccess}}',

  //QualityMeasures
  //QualityMeasures = '/api/QualityMeasures/{{siteId}}/{{reportingPeriod}}/{{rollingWeek}}/{{measureCd}}/{{locationCd}}/{{providerCd}}',
  QualityMeasuresGetLocations = '/api/QualityMeasures/GetLocations/{siteId}',
  
  //To Be Deleted---------------------------
  //GetRules = '/api/RulesSite/{siteId}/{{ruleId}}',
  //GetWorkFlow = '/api/WorkFlow/{{workflowId}}',
  //StatusWorkFlow = '/api/RulesSite/{{siteId}}/WorkFlowStatus/{{workflowId}}?guid={{workflowGuid}}',
  //GetRuleTypeModel = '/api/Rules/RuleDefinitionbyType/{{ruleTypeName}}',
}


export enum ApiTypes {
  V1 = "/V1",
  V2 = "/V2",
  AuthProvider = "/AuthProvider",
  PdfFiller = "/PdfFiller",
  None = ""
}

.dashboardWrapper{
  height: 100vh;
  display: flex;
  font-family: MuseoSans-300;
}

.dashboardSideNav{
  background-color: #0071BC !important;
  border: none !important;
}

.expandedSideNav{
  width: 340px;
  height: 97vh;
}

.expandedSideNav::before{
  content: ' ';
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0.8;
  z-index: -1;
  background-image: url("../../../../../assets/images/Pattern.svg");
}

.collapsedSideNav{
  width: 98px;
  height: 97vh;
}

.collapsedSideNav::before{
  content: ' ';
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0.8;
  z-index: -1;
  background-image: url("../../../../../assets/images/Pattern.svg");
}

.logoContainer{
  margin-top: 16px;
  background-image: url("../../../../../assets/images/Logo2024.svg");
  background-position: top left;
  background-repeat: no-repeat;
  padding-top: 32.5%;
}

.logoContainerCollapsed{
  margin-top: 16px;
  background-image: url("../../../../../assets/images/Chorus\ Logo_icon-hoz.svg");
  background-position: -26px 0px;
  background-repeat: no-repeat;
  padding-top: 122%;
}

.sidenavSubContainer{
 display: flex;
 flex-direction: column;
 justify-content: space-between;
 height: 78vh;
}

.dashboardTabs{
  display: flex;
  flex-direction: column;
  padding-top: 88px;
  padding-left: 15px;
  color: white;
}

.dashboardOption {
  display: flex;
  height: 55px !important;
  margin-bottom: 5px;
}

.dashboardTabItem{
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'MuseoSans-300';
  font-size: 16px;
}

.collapsedItem{
  display: flex;
  flex-direction: column;
  text-align: center;
  justify-content: center;
  font-size: 12px;
  line-height: 12px;
  font-family: 'MuseoSans-300';
}

.uncollapsedItem{
  justify-content: center;
  text-align: center;
  text-align: left;
  margin-top: -3px;
  padding-left: 15px;
}

.itemIcon{
  padding-right: 10px;
}

.collapsedIcon{
  padding: 0 0 1px 0;
}

.wrapText{
  text-wrap: wrap;
}

.dashboardOption[aria-selected="true"] {
  background-color: white;
  border-top-left-radius: 25px;
  border-bottom-left-radius: 25px;
}

.collapsedSideNav .dashboardOption{
    justify-content: center;
    text-align: center;
}

.dashboardOption[aria-selected="true"] .dashboardTabItem {
  color: #0071BC !important;
}

.dashboardOption[aria-selected="true"] .dashboardTabItemCollapsed {
  color: #0071BC !important;
}

.dashboardOption:hover {
  background-color: rgba(255,255,255,0.90);
  border-top-left-radius: 25px;
  border-bottom-left-radius: 25px;
  color: #0071BC !important;
}

.dashboardOption:hover .itemIcon{
  filter: invert(40%) sepia(81%) saturate(2337%) hue-rotate(187deg) brightness(68%) contrast(106%);
}

.dashboardOption:focus {
  background-color: white;
  border-top-left-radius: 25px;
  border-bottom-left-radius: 25px;
}

.isActive {
  background-color: white;
  border-top-left-radius: 25px;
  border-bottom-left-radius: 25px;
  color: #0071BC !important;
}

.isActive .itemIcon {
  filter: invert(40%) sepia(81%) saturate(2337%) hue-rotate(187deg) brightness(68%) contrast(106%);
}

#huddleIcon{
  height: 34px;
  width: 25px;
  padding-bottom: 3px;
}

.userSection{
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
}

.collapsedUserSection{
padding-bottom: 100px;
}

.userButton{
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: none;
  background: none;
  height: 100%;
  color: white !important;
  z-index: 1;
}

.avatar {
  background-color: #80a9c8;
  color: #fff;
  padding: 2px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius:100%;
  width:60px;
  height: 60px;
  font-weight: 600;
  font-size: 20px;
}

.avatarIcon{
  color: white !important;
  padding-top: 2px;
  padding-left: 3px;
}

.userInfo{
  display: flex;
  justify-content: center;
}

.userEmail{
  font-size: 12px;
}

.collapseIcon{
  position: absolute;
  top: 82vh;
  left: 0px;
}

.collapseButtonExpanded{
  height: 38px;
  width: 25px;
  background: #eff1f6;
  border-top-left-radius: 20px;
  border-bottom-left-radius: 20px;
  margin-left: 315px;
  padding: 6px 0 0;
  border: 0;
  color: #b7b7b7;
}

.collapseButtonCollapsed{
  height: 38px;
  width: 25px;
  background: #eff1f6;
  border-top-left-radius: 20px;
  border-bottom-left-radius: 20px;
  margin-left: 73px;
  padding: 6px 0 0;
  border: 0;
  color: #b7b7b7;
}

.updateIcon{
  height: 19px;
  width: 25px;
  background-image: url("../../../../../assets/images/update_FILL0_wght400_GRAD0_opsz24.svg");
  background-position: top center;
  background-repeat: no-repeat;
}

.siteButton{
  display: flex;
  justify-content: center;
  border-radius: 10px;
}

.siteName{
  color: white;
}

.dateLastLoaded{
  display: flex;
  padding-top: 40px;
  font-family: var(--mdc-typography-button-font-family, var(--mdc-typography-font-family, Roboto, sans-serif));
  font-size: 12px;
  color: aliceblue;
}

chorusVersion{
  justify-content: center;
  display: flex;
  padding-top: 40px;
  font-family: var(--mdc-typography-button-font-family, var(--mdc-typography-font-family, Roboto, sans-serif));
  font-size: 12px;
  color: aliceblue;
}


.dateText{
  font-family: 'MuseoSans-300';
}

.menuDivider{
  margin: 1px;
}

.menu-separator {
  height: 1px;
  background-color: rgba(255, 255, 255, 0.3);
  margin: 10px 15px;
  width: calc(100% - 30px);
}

.bonus-measures-icon {
  color: white !important;
   margin-top: 5px;
}

.isActive .bonus-measures-icon {
  color: #9c27b0 !important;
}

.dashboardOption:hover .bonus-measures-icon {
  color: #9c27b0 !important;
}

/* Ensure Bonus Measures menu item never shows active state */
.dashboardOption.bonus-measures-menu {
  background-color: transparent !important;
}

.dashboardOption.bonus-measures-menu .dashboardTabItem {
  color: white !important;
}

.dashboardOption.bonus-measures-menu:hover {
  background-color: rgba(255,255,255,0.90) !important;
  color: #0071BC !important;
}

.dashboardOption.bonus-measures-menu:hover .dashboardTabItem {
  color: #0071BC !important;
}

.contentWrapperExpanded{
  background-color: #0071BC;
  margin-left: 340px !important;
  width: 100%;
}

.contentWrapperCollapsed{
  background-color: #0071BC;
  margin-left: 98px !important;
  width: 100%;
}

.contentLayout{
  padding-left: 10px;
  padding-right: 10px;
  background-color: #eff1f6;
  height: 100vh;
  overflow-y: auto;
  flex-direction: column_reverse;
  justify-content: space-between;
  border-top-left-radius: 15px;
  border-bottom-left-radius: 15px;
}

.linkSpacerRight {
  padding-right: 20px;
}

.linkSpacerLeft {
  padding-left: 20px;
}

.showclickable {
  cursor: pointer;
  color: rgb(13, 13, 215);
  text-decoration: underline;
}



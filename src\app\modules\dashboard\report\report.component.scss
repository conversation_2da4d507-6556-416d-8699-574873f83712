// Report Dashboard with Left Stacked Panels
.report-wrapper {
  display: flex;
  flex-direction: column;
  height: 100vh;
  font-family: MuseoSans-300;
  background-color: #f8f9fa;
}

// Report Header
.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background-color: #fff;
  border-bottom: 1px solid #e0e0e0;
  z-index: 10;

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;

    .report-title {
      color: #0071BC;
      font-family: Museo500-Regular;
      margin: 0;
      font-size: 1.75rem;
      font-weight: 500;
    }

    .dynamic-panel-badge {
      display: flex;
      align-items: center;
      gap: 6px;
      background-color: #e3f2fd;
      color: #0071BC;
      padding: 6px 12px;
      border-radius: 16px;
      font-size: 0.8rem;
      font-weight: 500;

      mat-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;
      }
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

// Report Content Layout
.report-content-layout {
  display: flex;
  flex: 1;
  overflow: hidden;
  gap: 8px;
  padding: 8px;
}

// Left Sidebar Container
.left-sidebar {
  width: 350px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

// Sidebar Panel Base Styles
.sidebar-panel {
  background-color: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: flex 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  // When both panels are open, they split 50/50
  flex: 1;

  // When one panel is collapsed, the other expands
  &.expanded {
    flex: 2;
  }

  // When collapsed, panel takes minimal space
  &.collapsed {
    flex: 0 0 auto;
  }

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #fff;
    border-bottom: 1px solid #e0e0e0;
    cursor: pointer;

    &:hover {
      background-color: #f8f9fa;
    }

    h3 {
      margin: 0;
      font-size: 1rem;
      font-weight: 600;
      color: #333;
    }

    .panel-toggle-btn {
      color: #666;

      &:hover {
        background-color: rgba(0, 0, 0, 0.1);
      }

      mat-icon {
        font-size: 20px;
        width: 20px;
        height: 20px;
      }
    }
  }

  .panel-content {
    flex: 1;
    overflow: hidden;

    &.hidden {
      display: none;
    }
  }
}

// Reports Panel Specific Styles
.reports-panel {
  .panel-content {
    overflow-y: auto;

    // Custom scrollbar styling
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }
}

// Filters Panel Specific Styles
.filters-panel {
  .filters-container {
    height: 100%;
    overflow-y: auto;
    padding: 16px;
    background-color: white;

    // Custom scrollbar styling
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }
}

// Report Viewer Area (Right)
.report-viewer-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  // Ensure child components take full height
  > * {
    height: 100%;
    flex: 1;
  }
}

// Responsive Design
@media (max-width: 1200px) {
  .left-sidebar {
    width: 300px;
  }
}

@media (max-width: 768px) {
  .report-content-layout {
    flex-direction: column;
    gap: 4px;
    padding: 4px;
  }

  .left-sidebar {
    width: 100%;
    flex-direction: row;
    gap: 4px;

    .sidebar-panel {
      flex: 1;
      min-height: 200px;

      &.collapsed {
        flex: 0 0 50px;
        min-height: 50px;
      }

      &.expanded {
        flex: 2;
      }
    }
  }

  .report-viewer-area {
    border-radius: 4px;
  }

  .report-header {
    padding: 12px 16px;

    .header-left {
      .report-title {
        font-size: 1.4rem;
      }
    }
  }
}

// Accessibility improvements
@media (prefers-reduced-motion: reduce) {
  .left-sidebar,
  .sidebar-panel,
  .report-viewer-area {
    transition: none;
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .sidebar-panel {
    border: 2px solid #000;
  }

  .panel-header {
    border-bottom: 2px solid #000;
  }

  .report-viewer-area {
    border: 2px solid #000;
  }
}

// Focus styles for accessibility
.panel-toggle-btn:focus {
  outline: 2px solid #0071BC;
  outline-offset: 2px;
}

import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { ApiHand<PERSON> } from 'src/app/shared-services/ep-api-handler/ep-api-handler';
import { ApiRoutes, ApiTypes } from 'src/app/shared-services/ep-api-handler/api-option-enums';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { QualityMeasure } from 'src/app/shared-services/quality-measures/model/quality-measures-model';
import { mockQualityMeasures } from 'src/app/shared-services/quality-measures/mock-data';

@Injectable({
  providedIn: 'root'
})
export class QualityMeasuresService {
  constructor(
    private apiHandler: ApiHandler,
    private userContext: UserContext
  ) {}

  // Location and Provider API methods removed as they're not needed yet

  /**
   * Get quality measures
   * @returns Observable with quality measure data
   */
  public getMeasures(): Observable<QualityMeasure[]> {
    // For now, return mock data
    // In the future, this would call an API endpoint
    return of(mockQualityMeasures);
  }
}

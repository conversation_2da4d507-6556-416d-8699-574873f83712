
<div class="expandButtonWrapper" *ngIf="!layoutService.getShowMenu()">
  <button mat-menu-item class="collapseButton" (click)="layoutService.toggleShowMenu();">
    <mat-icon class="material-symbols-outlined">menu</mat-icon>
  </button>
</div>
<div *ngIf="layoutService.getShowMenu()" class="reportNavWrapper">
  <div>
    <div class="topButtonRow">
      <div class="linkSpacerRight"></div>
      <div>
        <button mat-menu-item class="collapseButton" (click)="layoutService.toggleShowMenu();">
          <mat-icon class="material-symbols-outlined">menu_open</mat-icon>
        </button>
      </div>
      <div class="linkSpacerRight showclickable" (click)="toggleSideExpand(true)">Expand</div>
      <div>|</div>
      <div class="linkSpacerLeft showclickable" (click)="toggleSideExpand(false)">Collapse</div>
    </div>
    <div class="itemsContainer">
      <ng-container *ngFor="let section of menuSections">
          <mat-expansion-panel #expansionPanel [expanded]="section.categoryNm === currentReportSection || allExpandState">
            <mat-expansion-panel-header>
              <mat-panel-title>{{section.categoryNm}}</mat-panel-title>
            </mat-expansion-panel-header>
            <mat-nav-list>
              <a mat-list-item *ngFor="let navItem of section.reportsForSection" title="{{navItem.reportName}}" class="listItems" [ngClass]="{activeReport: navItem.reportName === currentReport}" (click)="NavSelect(navItem)">
                <div class="reportText">{{navItem.reportName}}</div>
              </a>
            </mat-nav-list>
          </mat-expansion-panel>
      </ng-container>
    </div>
  </div>
</div>
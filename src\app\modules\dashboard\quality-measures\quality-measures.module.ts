import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { QualityMeasuresComponent } from './quality-measures.component';
import { QualityMeasureFiltersComponent } from './quality-measure-filters/quality-measure-filters.component';
import { QualityMeasuresService } from './services/quality-measures.service';
import { LayoutModule } from '../../shared-modules/layouts/layout.module';

/**
 * This module is a wrapper for the standalone QualityMeasuresComponent
 * It provides the QualityMeasuresService and makes the component available to other modules
 */
@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    LayoutModule
  ],
  providers: [
    QualityMeasuresService
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class QualityMeasuresModule { }

import { EventEmitter, Injectable } from '@angular/core';
import { IReportList } from 'src/app/shared-services/ep-api-handler/models/report-list.model'
import { ApiHandler } from 'src/app/shared-services/ep-api-handler/ep-api-handler';
import { ApiRoutes, ApiTypes } from 'src/app/shared-services/ep-api-handler/api-option-enums';
import { IMenuSections } from 'src/app/modules/shared-modules/layouts/models/menu-item.model';
import { catchError, map, Observable, of, Subject, Subscription, takeUntil, tap, timer } from 'rxjs';
import { HttpParams } from '@angular/common/http';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { ISite } from '../../models/site-model';
import { DatePipe } from '@angular/common';
import { NgxSpinnerService } from 'ngx-spinner';

@Injectable({
  providedIn: 'root'
})

//Layout Service allows the main Module components to Interacts with the layout components
export class LayoutService {
  public static showReportNav: boolean = true;
  public static expandSideNav: boolean = true;
  private currentDefaultSite;
  public reportNavData: IReportList | any = null;
  public menuSections: Subject<IMenuSections[]> = new Subject<IMenuSections[]>();
  private siteListSub$: Subject<ISite[]> = new Subject<ISite[]>();
  private params: HttpParams= new HttpParams();
  public spinnerTimeOut: EventEmitter<boolean> = new EventEmitter<boolean>();
  private timerSubscription: Subscription | null = null;
  private stopTimerSubject = new Subject<void>();
  private static dateLastUpdatedFormatted : string;
  private static previousDateTimeValue : number = 0;

  // Data load status message subject
  public dataLoadStatus: Subject<{message: string, icon: string}> = new Subject<{message: string, icon: string}>();

  lblDateLastUpdate : string = "Date Last Updated: {{time}} on {{date}}"
  datepipe: DatePipe = new DatePipe('en-US');

  //Loads our ApiService for making backend calls and the userContext which handles users current sessions/context.
  constructor(private userContext: UserContext, private apiHandler: ApiHandler, private spinner: NgxSpinnerService){
    this.userContext.getCurrentSite().subscribe(s => {
      this.currentDefaultSite = s;
    });
  }

  showSpinner() {
    this.spinner.show();
  }

  hideSpinner() {
    this.spinner.hide();
    if (this.timerSubscription) {
      this.stopTimer();
    }
  }


  /**
   * Starts a 30-second timer.
   * Emits an event if the timer completes without being stopped.
   */
  startTimer() {
    // Reset the stop subject
    this.stopTimerSubject = new Subject<void>();

    // Start the 20-second timer
    this.timerSubscription = timer(20000)
      .pipe(takeUntil(this.stopTimerSubject)) // Stop the timer if stopTimerSubject emits
      .subscribe({
        next: () => {
          this.spinnerTimeOut.emit(true); // Emit the event
        },
        complete: () => {
          console.log('Timer completed or stopped.');
          this.spinnerTimeOut.emit(false);
        },
      });

      this.stopTimerSubject.subscribe((stop) => {
        if (this.timerSubscription) {
          this.timerSubscription.unsubscribe(); // Clean up the subscription
          this.timerSubscription = null;
        }
      });
  }

  /**
   * Stops or cancels the timer.
   */
  stopTimer() {
    if (this.timerSubscription) {
      this.stopTimerSubject.next(); // Emit to stop the timer
    }
  }

  // API call function to fetch data for last updated
  public GetDateLastUpdated(siteId: string = '0'): Observable<string> {
    const now = Date.now();

    // If a no cached value exists and or it's has been longer than 30 minutes then the cache is stale.
    const isCacheStale = !LayoutService.dateLastUpdatedFormatted ||
                        (now - LayoutService.previousDateTimeValue) > (30 * 60 * 1000);

    if (!isCacheStale) {
      return of(LayoutService.dateLastUpdatedFormatted);
    }

    const effectiveSiteId = siteId === '0' ? this.currentDefaultSite : siteId;
    const url = ApiRoutes.GetDateofLastUpdate.toString().replace('{{siteid}}', effectiveSiteId);

    return this.userContext.apihandler.Get<Date>(ApiTypes.V2, url).pipe(
      map(date => this.formatDate(date)),
      tap(formatted => {
        LayoutService.dateLastUpdatedFormatted = formatted;
        LayoutService.previousDateTimeValue = now;
      }),
      catchError(error => {
        console.error('Error fetching or formatting date:', error);
        return of('Unavailable');
      })
    );
  }

  private formatDate(date: Date): string {
    const dateString = this.datepipe.transform(date, 'MMMM d, y');
    const timeString = this.datepipe.transform(date, 'h:mm a');
    let formattedDate = this.lblDateLastUpdate.toString();

    if (dateString) {
      formattedDate = formattedDate.replace('{{date}}', dateString);
    }
    if (timeString) {
      formattedDate = formattedDate.replace('{{time}}', timeString);
    }

    return formattedDate;
  }

  setShowMenu(showReportNav: boolean) {
    LayoutService.showReportNav = showReportNav;
  }

  toggleShowMenu() {
    LayoutService.showReportNav = !LayoutService.showReportNav;
  }

  getShowMenu(): boolean {
    return LayoutService.showReportNav;
  }

  setExpandSideNav(expandSideNav: boolean) {
    LayoutService.expandSideNav = expandSideNav;
  }

  toggleExpandSideNav() {
    LayoutService.expandSideNav = !LayoutService.expandSideNav;
  }

  getExpandSideNav(): boolean {
    return LayoutService.expandSideNav;
  }

  //Returns menu data Api data from report endpoing
  public loadNavMenu() {
    this.userContext.apihandler.Get<IMenuSections>(ApiTypes.V2, ApiRoutes.Report, true, false, undefined, this.params).subscribe((data) => {
        this.reportNavData = data;
        this.groupMenuItemsToCategorys();
    });
  }

  public setNavandReload(siteId: number){
    if (siteId!==undefined)
    {
    this.params = new HttpParams({fromObject: {siteId: siteId}})
    this.loadNavMenu();
    }
  }

  //Handles Mapping Chorus Reports to Nav Menu by Categorys
  public groupMenuItemsToCategorys() {
    let menuFormat: IMenuSections[] = [] as IMenuSections[];
    let pastCategory: number[] = [];

    if (this.reportNavData !== null && this.reportNavData !== undefined) {
      this.reportNavData.forEach((item: IReportList) => {
        if (pastCategory.indexOf(item.categoryId) < 0) {
          pastCategory.push(item.categoryId);
          let sectionItems: IReportList[] =  this.reportNavData.filter((subList: IReportList) => subList.categoryId == item.categoryId);
        menuFormat.push({
            categoryId: item.categoryId,
            categoryNm: item.categoryNm, reportsForSection: sectionItems
          });
        }
      });
      this.menuSections.next(menuFormat);
    }
  }

  public setSites(sites: ISite[])
  {
    this.siteListSub$.next(sites);
  }

  /**
   * Sets the data load status message, stores it in session storage, and emits it to subscribers
   * @param message The message to display
   * @param icon The icon to display
   */
  public setDataLoadStatus(message: string, icon: string) {
    // Store in session storage to persist across page reloads
    sessionStorage.setItem('dataLoadStatusMessage', message);
    sessionStorage.setItem('dataLoadStatusIcon', icon);

    // Emit to subscribers
    this.dataLoadStatus.next({ message, icon });
  }

  /**
   * Gets the current data load status from session storage
   * @returns The current data load status
   */
  public getDataLoadStatus(): {message: string, icon: string} {
    return {
      message: sessionStorage.getItem('dataLoadStatusMessage') || '',
      icon: sessionStorage.getItem('dataLoadStatusIcon') || ''
    };
  }
}

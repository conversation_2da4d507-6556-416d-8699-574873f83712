import { ChangeDetector<PERSON><PERSON>, <PERSON>mpo<PERSON>, Inject, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild, AfterViewInit, HostListener } from '@angular/core';
import { FormBuilder, FormGroup, FormArray, Validators, FormControl } from '@angular/forms';
import { ExecutorTransaction, FlatNode, FormField, FormModel, JsonNode, PdfFillerRule, Rule, WorkFlowProcess, FormStatus } from '../models/dynamic-form-model';
import { catchError, forkJoin, map, Observable, of, Subscription, tap } from 'rxjs';
import { DynamicFormsService } from './services/dynamicForms/dynamic-forms-service';
import { customValidator } from './services/dynamicForms/custom-validator';
import { convertToParamMap, ActivatedRoute, ParamMap, Router } from '@angular/router';
import { MatTableDataSource } from '@angular/material/table';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { ApiRoutes, ApiTypes } from 'src/app/shared-services/ep-api-handler/api-option-enums';
import { MatTreeFlatDataSource, MatTreeFlattener } from '@angular/material/tree';
import { FlatTreeControl } from '@angular/cdk/tree';
import { MatStepper } from '@angular/material/stepper';
import { DialogResponseComponent } from '../../shared-modules/layouts/dialogs/dialog-response/dialog-response.component';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { LayoutService } from '../../shared-modules/layouts/services/layout/layout.service';
import { Time } from '@angular/common';
import { EpividianCommon } from '../../utility/EpividianCommon';
import { IframeJwtService } from 'src/app/shared-services/iframe-communication/iframe-jwt.service';
import { RulesEngineService } from '../../admin/services/rules-engine.service';
import { Workflow } from '../../admin/models/rule.model';

//import testjson from '../../../../../mockapi/data/FormsSample.json';

@Component({
  selector: 'app-dynamic-forms',
  templateUrl: './dynamic-forms.component.html',
  styleUrl: './dynamic-forms.component.scss'
})
export class DynamicFormsComponent implements OnInit, OnDestroy, AfterViewInit {
  Rules: Rule[] = [];
  dynamicForm!: FormGroup;
  formModel!: FormModel;
  forms: FormModel[] = [];
  form: FormField[][] = [];
  fields: FormField[] = [];
  pattern: RegExp = new RegExp("");
  formName: string = "";
  selectedDropValue: string = "";
  lastForm: boolean = false;
  submitted: boolean = false;
  formIndex: number = 0;
  panelOpenState: boolean = false;
  formNames: { [key: string]: string } = {};
  formExpandedState: { [key: string]: boolean } = {};
  formsSubmitted: { [key: string]: {isSigned: boolean, submitted: boolean} } = {};
  snapshotDynamicFormFields: { [key: string]: string } = {};
  workFlowId: number = 0;
  workFlowProcess: WorkFlowProcess = {} as WorkFlowProcess;
  highlightedField: string | null = null;
  queryParamsObject: { [key: string]: any } = {};
  tableDataSource: { [tableName: string]: MatTableDataSource<any> } = {};
  tableColumns: { [tableName: string]: string[] } = {};
  pdfFillerUrl: SafeResourceUrl[] = [];
  pdfButtonLabel: string = "Create PDF";
  WorkFlowProcessId: string = "";
  resultData: any = {};
  trackerPositionIndex = 0;
  WorkFlowDescription: string = "";
  PdfFillerRules: Rule[] = [];
  siteId: string = "";
  formsStatus: FormStatus[] = [];
  fillFormGroup: FormGroup;
  signFormGroup: FormGroup;
  pageSubscriptions = new Subscription();
  lastSubmitted: Date = new Date();
  formUserSubmitted: string = "";
  writeFileToWorkflowRule: Rule[] = [];
  apiRequestRules: Rule[] = [];
  apiMapRule: Rule = {} as Rule;
  apiResultStatus: boolean = false;
  openDialog: MatDialogRef<DialogResponseComponent,any> = {} as MatDialogRef<DialogResponseComponent, any>
  leadId: string = "";
  initialSubmit: boolean = false;
  isHistory: boolean = false;
  lastSpinnerShowTime: Date;
  retriesLeft: number = 3;
  renderPdfSection: boolean[] = [];
  isPdfLoaded: boolean = false;
  pdfLoadedStatus: boolean[] = [];
  pdfLoadTimeouts: any[] = [];
  programmaticNavigation: boolean = false;
  pdfDownloading: boolean = false;

  @ViewChild('stepper') stepper!: MatStepper;

  /**
   * Lifecycle hook that is called after Angular has fully initialized the component's view.
   * Used to disable forward navigation on the stepper headers.
   */
  ngAfterViewInit() {
    // Disable forward navigation by adding a class to the forward step headers
    this.disableForwardStepHeaders();
  }

  /**
   * Adds the disable-forward-step class to all step headers except the first one
   * to prevent forward navigation by clicking on the stepper
   */
  private disableForwardStepHeaders() {
    // Wait for the DOM to be fully rendered
    setTimeout(() => {
      // Get all step headers
      const stepHeaders = document.querySelectorAll('.mat-horizontal-stepper-header');

      // Skip the first header (index 0) and add the class to the rest
      for (let i = 1; i < stepHeaders.length; i++) {
        stepHeaders[i].classList.add('disable-forward-step');
      }
    });
  }

  constructor(
    public dialog: MatDialog,
    private userContext: UserContext,
    private formBuilder: FormBuilder,
    private FormService: DynamicFormsService,
    private activeRoute: ActivatedRoute,
    private sanitizer: DomSanitizer,
    private router: Router,
    private changeDetector: ChangeDetectorRef,
    private layoutService: LayoutService,
    private epividianCommon: EpividianCommon,
    private iframeJwtService: IframeJwtService,
    private rulesEngineService: RulesEngineService
  ) {
    this.fillFormGroup = this.formBuilder.group({
      fillCtrl: ['', Validators.required]
    });
    this.signFormGroup = this.formBuilder.group({
      signCtrl: ['', Validators.required]
    });

    this.lastSpinnerShowTime = this.getFutureTime(55);

    this.pageSubscriptions.add(
      userContext.getChorusUserInfo().subscribe((user) => {
        this.formUserSubmitted = user.lastNm + ", " + user.firstNm;
      }
    ));
  }


  //#region Initialization Methods

  /**
   * Load workflow details from the Rules Engine API
   * @param workflowId The workflow ID to fetch details for
   * @returns Observable of Workflow
   */
  private loadWorkflowDetails(workflowId: number): Observable<Workflow> {
    return this.rulesEngineService.getWorkflowById(workflowId).pipe(
      catchError(error => {
        //console.error('Error loading workflow details:', error);
        // Return a default workflow object if the API call fails
        return of({
          workflowID: workflowId,
          name: 'Workflow Form',
          description: '',
          type: '',
          createdDate: new Date(),
          modifiedDate: new Date()
        } as Workflow);
      })
    );
  }

  /**
   * OnInit lifecycle hook.
   * Initializes the component and sets up the dynamic form.
   */
  ngOnInit() {
    let pdfFillerUrl = this.userContext.apihandler.envUrlFromType(ApiTypes.PdfFiller);
        window.addEventListener('message', (event) => {
          this.epividianCommon.upsertToStorage("iframeInit", "true");
          if (event.origin !== pdfFillerUrl) return; // Security check

          if (event.data.action === 'SubmissionResponse') {
             // PDF was properly signed and submitted successfully
              let formId = event.data.data.split("-")[1];
              // Only mark as signed and submitted when we get SubmissionResponse (meaning properly signed)
              this.layoutService.hideSpinner();
              if (event.data.signatureDetails.details.hasInkSignatureChanges != undefined && event.data.signatureDetails.details.hasInkSignatureChanges == true)
              {
                this.formsSubmitted[formId] = { isSigned: true, submitted: true };
              }
              let nextSectionId = Number(formId) + 1;
              if (this.PdfFillerRules.length > nextSectionId && this.formExpandedState[nextSectionId] === false)
              {
                this.toggleSection(nextSectionId);
              }
          } else if (event.data.action === 'PdfLoaded') {
              // Handle PDF loaded message from iframe
              const pdfIndex = event.data.formId || 0;

              // Clear any timeout for this PDF
              if (this.pdfLoadTimeouts[pdfIndex]) {
                clearTimeout(this.pdfLoadTimeouts[pdfIndex]);
              }

              // Mark this PDF as loaded and reset downloading flag
              this.pdfLoadedStatus[pdfIndex] = true;
              this.pdfDownloading = false;

              // Check if all PDFs are loaded
              const allPdfsLoaded = this.pdfLoadedStatus.every(status => status === true);
              if (allPdfsLoaded) {
                this.isPdfLoaded = true;
                this.layoutService.hideSpinner();
              }
          } else if (event.data.action === 'PdfDownloading') {
              // Handle PDF downloading message from iframe
              const pdfIndex = event.data.formId || 0;

              // Mark PDF as downloading and show spinner
              this.pdfDownloading = true;
              this.layoutService.showSpinner();

              // Reset the loaded status for this PDF
              if (this.pdfLoadedStatus[pdfIndex]) {
                this.pdfLoadedStatus[pdfIndex] = false;
              }
          }

          // this.stepper.next();
          // Handle legacy PdfLoaded event format
          if (event.data.action === "PdfLoaded" && !event.data.formId)
          {
            this.isPdfLoaded = true;

            // If we're in history mode, find which PDF iframe sent the message
            if (this.isHistory) {
              // Try to determine which PDF was loaded
              const iframes = document.querySelectorAll('iframe[id^="pdfFiller_"]');
              iframes.forEach((iframe, index) => {
                if ((iframe as HTMLIFrameElement).contentWindow === event.source) {
                  this.pdfLoadedStatus[index] = true;

                  // Clear any timeout for this PDF
                  if (this.pdfLoadTimeouts[index]) {
                    clearTimeout(this.pdfLoadTimeouts[index]);
                  }
                }
              });

              this.layoutService.hideSpinner();
            } else {
              // For non-history mode, just hide the spinner
              this.layoutService.hideSpinner();
            }
          }

          /*
          //  *** Handle PDF downloading message (for history mode) ***
          if (event.data.action === "PdfDownloading")
          {
            //console.log('PDF downloading message received from iframe');

            // If we're in history mode, find which PDF iframe sent the message
            if (this.isHistory) {
              // Try to determine which PDF is downloading
              const iframes = document.querySelectorAll('iframe[id^="pdfFiller_"]');

              iframes.forEach((iframe, index) => {
                if ((iframe as HTMLIFrameElement).contentWindow === event.source) {
                  //console.log(`PDF ${index} downloading`);
                  // We don't change the loading status yet, just log it
                }
              });
            }
          }
          */

      });

      this.epividianCommon.upsertToStorage("iframeInit", "false");




    this.layoutService.showSpinner();

        // Capture the showTitle query parameter
        const showTitleParam = this.activeRoute.snapshot.queryParamMap.get('showTitle');

    //Gets the WorkFlowId from the URL
    if (this.activeRoute.snapshot.paramMap.get('workFlowId') != null) {
      this.workFlowId = +this.activeRoute.snapshot.paramMap.get('workFlowId')!;
    }

    //Gets the siteId from the URL
    if (this.activeRoute.snapshot.paramMap.get('siteId') != null) {
      var siteIsNumber = +this.activeRoute.snapshot.paramMap.get('siteId')!;
      this.siteId = siteIsNumber.toString();
      this.queryParamsObject["SITEID"] = this.siteId;
    }

            // Capture the WorkFlowId query parameter
    let workFlowProcessId = this.activeRoute.snapshot.queryParamMap.get('WorkFlowId');

    // Capture the WorkFlowId query parameter
    let isHistory = this.activeRoute.snapshot.queryParamMap.get('IsHistory');
    if (isHistory != null && (isHistory == "1" || (typeof isHistory === 'string' && isHistory.toLowerCase() == "true"))) {
        this.isHistory = true;
    }

    this.dynamicForm = this.formBuilder.group({});

    // Retrieve all query parameters from the URL
    const queryParams = this.activeRoute.snapshot.queryParamMap;

    // Loop through each query parameter and add it to the queryParamsObject
    queryParams.keys.forEach((key: string) => {
      let ucaseKey = key.toUpperCase();
      this.queryParamsObject[ucaseKey] = queryParams.get(key)?.toUpperCase();
    });

    if (workFlowProcessId == null) {
      workFlowProcessId = "";
    }
    else
    {
      this.isHistory = true;
    }

    // Load the workflow details first, then load the form data
    this.pageSubscriptions.add(
      this.loadWorkflowDetails(this.workFlowId).subscribe((workflow: Workflow) => {
        // Load the form data from the workflow
        this.pageSubscriptions.add(
          this.FormService.ExecuteWorkFlow(this.siteId, this.workFlowId, workFlowProcessId, "", queryParams).subscribe((response: WorkFlowProcess) => {
            this.workFlowProcess = response;
            this.WorkFlowProcessId = response.workFlowProcessId;

            // Keep workFlowTextCode hardcoded for now (will be replaced with new field in future)
            this.workFlowProcess.workFlowTextCode = "{0__FIRSTNAME} {0__LASTNAME} (MRN {0__MRN})";

            // Use workflow name from the Rules Engine API, fallback to response if not available
            this.formName = workflow.name || response.workFlowName || 'Workflow Form';

            // Use the hardcoded workFlowTextCode for description (until new field is added)
            this.WorkFlowDescription = this.workFlowProcess.workFlowTextCode;

            let workFlowTextCodes = this.extractVariables(this.workFlowProcess.workFlowTextCode || '');

        const ruleIdsWithStatusZero = response.workFlowRuleDependencies
              .filter(dependency => dependency.Status == 0 || dependency.Status==3) // Filter by Status 0
              .sort((a, b) => a.SequenceOrder - b.SequenceOrder) // Sort by SequenceOrder
              .map(dependency => dependency.RuleId); // Map to extract RuleId

        const matchedRules: Rule[] = response.RuleDetails
              .filter(rule => ruleIdsWithStatusZero.includes(rule.RuleID) && rule.Type && rule.Type.toLowerCase() == "formrule")
              .sort((a, b) => ruleIdsWithStatusZero.indexOf(a.RuleID) - ruleIdsWithStatusZero.indexOf(b.RuleID));

        this.PdfFillerRules  = response.RuleDetails.filter(rule => rule.Type && rule.Type.toLowerCase() == "pdffillerrule");
        this.writeFileToWorkflowRule = response.RuleDetails.filter(rule => rule.Type && rule.Type.toLowerCase() == "writefiletoworkflowrule");
        this.apiMapRule = response.RuleDetails.filter(rule => rule.Type && rule.Type.toLowerCase() == "maptransformrule")?.[0];
        this.apiRequestRules = response.RuleDetails.filter(rule => rule.Type && rule.Type.toLowerCase() == "apirequesterrule");

        let dataUrl: { [formName: string]: string } = {};
        let savedData: any[] = [];
        let formRules: any[] = [];

        // Process the matched rules
        matchedRules.forEach(rule => {
          let formFields = JSON.parse(rule.JsonData);
          rule.JsonData = formFields;
          formRules.push(rule);

          // Check if the rule has executed/saved data
          var ruleHasExecutedData =  this.workFlowProcess.executorTransactions.filter(transactions=>transactions!=null).some(transaction => transaction.RuleId === rule.RuleID);
          // if (formFields.DataUrl != null && formFields.DataUrl != "" && ruleHasExecutedData == false) {
          if (formFields.DataUrl != null && formFields.DataUrl != "") {
            dataUrl[formFields.FormName] = formFields.DataUrl;
          }

          // If the rule has executed data, add it to the savedData array. Used to populate the form fields.
          if (ruleHasExecutedData) {
            savedData.push(this.workFlowProcess.executorTransactions.filter(transactions=>transactions!=null).find(transaction => transaction.RuleId === rule.RuleID));
          }
        });

        //used to force load and re-render the iframe pdf section
        this.renderPdfSection.push(true);
        for(let pdfId=1; pdfId < this.PdfFillerRules.length; pdfId++)
        {
          this.renderPdfSection.push(false);
        }



        this.combineRules(formRules);
        this.parseJsonData();


        // Load form data from the specified URLs and populate the form fields initially.
      const formDataObservables = this.forms.map(form => {
        const indexId = this.forms.findIndex(x => x.FormName === form.FormName);
        return this.getFormNames(dataUrl, form.FormName).map(formName => {
          return this.getFormData(dataUrl[formName]).pipe(
            map((result: any) => {
              const formData = result.result[0];
              form.Fields.forEach(field => {
                const fieldName = field.FieldName.toUpperCase();

                for (let key in formData) {
                  if (indexId.toString() + "__" + key.toUpperCase() === fieldName) {
                    if (field.FieldType === "checkbox") {
                      // Implement checkbox handling logic here if needed
                    } else if (field.FieldType === "date") {
                      const dateValue = this.stripDate(formData[key]);
                      this.dynamicForm.controls[field.FieldName].setValue(dateValue);
                      this.dynamicForm.controls[field.FieldName].markAsTouched();
                      break;
                    } else {
                      this.dynamicForm.controls[field.FieldName].setValue(formData[key]);
                      this.dynamicForm.controls[field.FieldName].markAsTouched();
                      break;
                    }
                  }
                }
              });

              // Update workflow descriptions if necessary
              for (let key in formData) {
                const uKey = indexId.toString() + "__" + key.toUpperCase();
                if (workFlowTextCodes.includes(uKey)) {
                  this.updateWorkFlowDescription(uKey, formData[key]);
                }
              }


            })
          );
        });
      });

      // Use forkJoin to wait for all observables to complete

      this.pageSubscriptions.add(
        forkJoin(formDataObservables.flat()).subscribe(() => {
          // Now load the saved data into the form fields after all form data has been loaded
          savedData.forEach((formEntry) => {
            const parsedSavedData = JSON.parse(formEntry.RuleInputJson);
            const matchingForm = this.forms.find((form) => form.FormName === parsedSavedData.FormName);

            if (matchingForm) {
              matchingForm.Fields.forEach((field) => {
                try {
                const fieldNameUpper = field.FieldName.toUpperCase();
                const savedFieldData = parsedSavedData.Fields.find(
                  (data: FormField) => data.FieldName.toUpperCase() === this.splitFormIndex(fieldNameUpper)
                );

                if (savedFieldData) {

                  if (field.FieldType === "checkbox") {

                    const checkboxArray = this.dynamicForm.get(field.FieldName) as FormArray
                    // Ensure the checkbox field exists as a FormArray in the dynamic form
                    const index = this.getKeyIndex(field.Attributes,savedFieldData.SelectedValue);

                    if (index !== null && index >=  -1 && checkboxArray) {
                        checkboxArray.at(index).setValue(true);
                        checkboxArray.at(index).markAsTouched();
                    }
                  } else if (field.FieldType === "dropdown") {
                    this.dynamicForm.controls[field.FieldName].setValue(savedFieldData.SelectedValue);
                    this.dynamicForm.controls[field.FieldName].markAsTouched();
                  } else if (field.FieldType !== "table") {
                    this.dynamicForm.controls[field.FieldName].setValue(savedFieldData.SelectedValue);
                    this.dynamicForm.controls[field.FieldName].markAsTouched();
                  }
                  if (workFlowTextCodes.includes(fieldNameUpper)) {
                    this.updateWorkFlowDescription(fieldNameUpper, savedFieldData.SelectedValue);
                  }
                }
              }
              catch (error) {
                //console.log("Error loading saved data: ", error);
              }
              });
            }
          });
          this.layoutService.hideSpinner();

          this.forms.forEach((form, frmIndex) => {
            if (frmIndex==3) {
              let v = "v";
            }
            form.Fields.forEach(field => {
              let control = this.dynamicForm.controls[field.FieldName];
              // If the model has a non-empty SelectedValue, set it now
              if (control && control.value == "" && field.SelectedValue != null) {
                control.setValue(field.SelectedValue);
                control.markAsTouched();
              }
            });
          });

          this.saveFormSnapshot("init");
          if (this.isHistory)
          {
            this.dynamicForm.clearValidators();
            this.dynamicForm.disable();
          }



        })
      );



          })
        );
      })
    );
}


  onStepChange(event: any) {
    // Check if trying to navigate forward by clicking on the stepper
    if (!this.programmaticNavigation && event.previouslySelectedIndex < event.selectedIndex) {
      // Prevent forward navigation by clicking on the stepper
      setTimeout(() => {
        this.stepper.selectedIndex = event.previouslySelectedIndex;
      });
      return;
    }

    // Handle going back from a later step to an earlier step
    if (event.previouslySelectedIndex > event.selectedIndex) {
      // If going back from Sign step (1) to Fill step (0)
      if (event.previouslySelectedIndex === 1 && event.selectedIndex === 0) {
        // Reset the PDF signing status since the user will need to recreate PDFs
        this.formsSubmitted = {}; // Clear all signing statuses

        // Reset the PDF rendering state
        this.renderPdfSection = [];
        this.renderPdfSection.push(true);
        for(let pdfId=1; pdfId < this.PdfFillerRules.length; pdfId++) {
          this.renderPdfSection.push(false);
        }

        // If in history mode, also disable the form
        if (this.isHistory) {
          this.dynamicForm.clearValidators();
          this.dynamicForm.disable();
        }
      }

      // If in history mode and going to Sign step
      else if (this.isHistory && event.selectedIndex === 1) {
        this.createPdfs();
      }
    }
  }

  saveFormSnapshot(formStatus: string|null = null): void {
    // Clear the snapshotDynamicFormFields before taking the new snapshot
    this.snapshotDynamicFormFields = {};

    // Iterate over the controls in the dynamicForm
    Object.keys(this.dynamicForm.controls).forEach(controlName => {
      // Get the current value of the control
      const controlValue = this.dynamicForm.get(controlName)?.value;

      // Save the controlName and its value in snapshotDynamicFormFields
      this.snapshotDynamicFormFields[controlName] = controlValue;
    });

    if (formStatus != null) {
      this.updateFormStatus("", formStatus, true);
    }
  }

  // Helper method to retrieve all form names
  getFormNames(dataArray: any, formName: string): string[] {
    let keys = Object.keys(dataArray);

    let id = keys.findIndex(key => key == formName);

    if (id >= 0) {
      return [keys[id]];
    }
    else
    {
      return [];
    }

  }


  splitFormIndex(input: string): string {
    const splitParts = input.split('__');
    if (splitParts.length >= 2) {
      return splitParts[1];
    } else {
      return input; // Handle case where there's only one part or no match
    }
  }

  //#endregion

  //#region Data Handling Methods

  /**
   * Combines the rules from the results into a single array.
   * @param results An array of rules to be combined.
   */
  combineRules(results: any[]) {
    results.forEach(result => {
      this.Rules.push(result);
      let fn = result.JsonData.FormName;
      this.formNames[fn] = result.Name;
    });
  }

  /**
   * Retrieves form data from the specified URL.
   * @param dataUrl The URL to fetch form data from.
   * @returns An observable containing the form data.
   */
  getFormData(dataUrl: string): Observable<any> {
    let urlUpdated = false;
    dataUrl = dataUrl.replace(/{([^}]+)}/gi, (match, p1) => {
      const replacementValue = this.queryParamsObject[p1.toUpperCase()];
      if (replacementValue !== undefined) {
        urlUpdated = true;
        return replacementValue;
      } else {
        urlUpdated = false;
        return match;
      }
    });

    if (urlUpdated) {
      return this.FormService.GetRuleData(dataUrl, this.workFlowId, this.WorkFlowProcessId);
    } else {
      return new Observable();
    }
  }


  //#endregion

  //#region Form Handling Methods

  /**
   * Parses JSON data to populate form rules.
   */
  parseJsonData() {
    this.Rules.forEach((rule: Rule) => {
      this.forms.push(JSON.parse(JSON.stringify(rule.JsonData)));

      this.buildForm(this.formIndex);
      this.formExpandedState[this.formIndex] = this.formIndex==0 ? true : false;
      this.formIndex++;
    });
  }

  /**
   * Builds the form dynamically based on the fields.
   * @param frmIndex The index of the form to build.
   */
  buildForm(frmIndex: number) {
    this.fields = this.forms[frmIndex].Fields;
    this.form = this.groupFormFields(this.fields);

    // Check if formsStatus already contains an entry for this formId, if not, add one with status 'init'
    const existingStatus = this.formsStatus.find(status => status.formId === frmIndex.toString());
    if (!existingStatus) {
      this.formsStatus.push({
        formId: frmIndex.toString(),
        formStatus: 'init' // Initial status set to 'init'
      });
    }

    this.fields.forEach(field => {
      field.FieldName = frmIndex.toString() + "__" + field.FieldName;

      if (!this.dynamicForm.contains(field.FieldName)) {
        let control;
        if (field.FieldType === "checkbox") {
          // Initialize checkbox form array
          if (!field.Attributes) {
            field.Attributes = { 0: field.Label };
          }

          const checkboxArray = this.formBuilder.array(
            Object.keys(field.Attributes).map(() => new FormControl({ value: false, disabled: !field.isEditable }))
          );

          control = checkboxArray;
        } else if (field.FieldType === 'table') {
          this.initializeTable(field.FieldName, field);
        }
        else {
          // Check if field is editable and set the disabled state accordingly
          control = new FormControl(
            { value: field.SelectedValue || '', disabled: !field.isEditable },
            customValidator(this.forms[frmIndex], field, this.dynamicForm, frmIndex.toString() + "__")
          );
        }

        if (field.FieldName.includes("3__")) {
          var test = "test";
        }

        if (field.FieldType !== 'table') {
          this.dynamicForm.addControl(field.FieldName, control);
        }
      }
    });
  }


  // Method to update form status
updateFormStatus(formId: string, status: string, doAll:boolean = false): void {
  if (doAll) {
    // Update all form statuses to the specified status
    this.formsStatus.forEach(formStatus => {
      formStatus.formStatus = status;
    });
  }
  else
  {
    const formStatus = this.formsStatus.find(status => status.formId === formId);
    if (formStatus) {
      formStatus.formStatus = status; // Update the form status to 'modified'
    }
  }
}



  /**
   * Gets the appropriate CSS class for a given input type.
   * @param type The type of the input field.
   * @returns The corresponding CSS class.
   */
  getClass(type: string): string {
    if (!type) {
      return "";
    }

    let className = "";
    switch (type.toLowerCase()) {
      case 'text':
        className = "textInput";
        break;
      case 'number':
        className = "numberInput";
        break;
      case 'checkbox':
        className = "checkboxInput";
        break;
      case 'date':
        className = "dateInput";
        break;
      case 'textarea':
        className = "textareaInput";
        break;
      case 'dropdown':
        className = "selectInput";
        break;
      default:
        //console.log('Unknown input type: ' + type);
    }
    return className;
  }

  //#endregion

  //#region Helper Methods

  /**
   * Groups form fields by their layout options.
   * @param ff An array of form fields to group.
   * @returns A grouped array of form fields.
   */
  public groupFormFields(ff: FormField[]): FormField[][] {
    const groups: { [key: string]: FormField[] } = {};

    ff.forEach(field => {
      if (!groups[field.LayoutOption]) {
        groups[field.LayoutOption] = [];
      }
      groups[field.LayoutOption].push(field);
    });

    return Object.values(groups);
  }



  //#endregion

  //#region Event Handlers

  /**
   * Handles changes in the dropdown field.
   * @param field The field object being changed.
   * @param $event The event object triggered by the change.
   */
  onDropdownChange(field: FormField, $event: any) {
    var temp = $event.target.value.split(': ')[1];
    field.SelectedValue = temp;
  }

  /**
   * Handles changes in the checkbox field.
   * @param field The field object being changed.
   * @param value The value of the checkbox.
   * @param $event The event object triggered by the change.
   */
  onCheckboxChange(field: FormField, value: any, $event: any) {
      if (field.SelectedValue != "" && field.SelectedValue) {
        if (field.SelectedValue.includes(value)) {
          if (field.SelectedValue.includes("| " + value)) {
            field.SelectedValue = field.SelectedValue.replace(" | " + value, "");
          } else if (field.SelectedValue.includes(value + " |")) {
            field.SelectedValue = field.SelectedValue.replace(value + " | ", "");
          } else {
            field.SelectedValue = "";
          }
        } else {
          field.SelectedValue += " | " + value;
        }
      } else {
        field.SelectedValue = value;
      }
  }

  onRadioClick(field: FormField, value: any, $event: any) {
    if (field.SelectedValue != "" && field.SelectedValue) {
      if (field.SelectedValue == value){
        field.SelectedValue = "";
        this.dynamicForm.controls[field.FieldName].reset();
      }
      else{
        field.SelectedValue = value;
      }
    } else {
      field.SelectedValue = value;
    }
}

  back(): void {
    // Set history mode to true to make the form read-only
    this.isHistory = true;

    // Set programmatic navigation flag to true before navigating
    this.programmaticNavigation = true;

    // Always navigate to step 1 (Sign step) when View Form is clicked
    this.stepper.selectedIndex = 1;

    // Reset the flag after a short delay
    setTimeout(() => {
      this.programmaticNavigation = false;
    }, 100);

    // Create PDFs for the Sign step
    this.createPdfs();

    // Update URL with history flag
    if (this.WorkFlowProcessId) {
      // If 'WorkFlowId' exists in the current URL, navigate back to the same URL to force refresh
      this.router.navigate([], {
        queryParams: { WorkFlowId: this.WorkFlowProcessId, IsHistory: 1 },
        queryParamsHandling: 'merge' // Keeps other query parameters if any
      });
    } else {
      // If 'WorkFlowId' does not exist, add it to the URL as a query parameter
      this.router.navigate([], {
        queryParams: { WorkFlowId: this.workFlowProcess.workFlowProcessId, IsHistory: 1 },
        queryParamsHandling: 'merge' // Keeps other query parameters if any
      });
    }

  }

  noSort = (a: any, b: any) => 0;
  //#endregion

  //#region Utility Methods

  /**
   * Tracks items by their index in the list.
   * @param index The index of the item.
   * @param item The item to track.
   * @returns The index of the item.
   */
  trackByIndex(index: number, item: any): any {
    return index;
  }

  /**
   * Tracks fields by their field name.
   * @param index The index of the field.
   * @param field The form field to track.
   * @returns The field name.
   */
  trackByFieldName(index: number, field: FormField): any {
    return field.FieldName;
  }

  /**
   * Retrieves a value from a dictionary based on a provided key.
   * @param dictionary The dictionary to search.
   * @param key The key to look up.
   * @returns The value associated with the key, or null if not found.
   */
  getDictionaryValue(dictionary: { [key: string]: any }, key: string): any {
    if (dictionary.hasOwnProperty(key)) {
      return dictionary[key];
    } else {
      return null;
    }
  }

  /**
   * Highlights a control based on the specified field.
   * @param field The field to highlight.
   */
  highlightControl(field: FormField): void {
    if (this.highlightedField === field.FieldName) {
      this.highlightedField = null; // Unhighlight
    } else {
      this.highlightedField = field.FieldName;
      setTimeout(() => {
        const controlElement = document.querySelector(`[ng-reflect-name="${field.FieldName}"]`);
        if (controlElement) {
          (controlElement as HTMLElement).focus();
        }
      }, 0);
    }
  }

  /**
   * Handles document clicks to remove field highlighting when clicking anywhere on the page
   * @param event The click event
   */
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent): void {
    // Check if we have a highlighted field
    if (this.highlightedField) {
      // Get the clicked element
      const clickedElement = event.target as HTMLElement;

      // Check if the clicked element is a checklist item or a form field
      // We don't want to remove highlighting when clicking on these elements
      // as they have their own click handlers for highlighting
      const isChecklistItem = clickedElement.closest('.infoItem') !== null;
      const isFormField = clickedElement.closest('.fieldWrapper') !== null;

      // Only remove highlight if clicking outside both checklist items and form fields
      if (!isChecklistItem && !isFormField) {
        this.highlightedField = null;
      }
    }
  }

  /**
   * Checks if a field is currently highlighted.
   * @param fieldName The name of the field to check.
   * @returns A boolean indicating whether the field is highlighted.
   */
  isFieldHighlighted(fieldName: string): boolean {
    return this.highlightedField === fieldName;
  }

  /**
   * Initializes a table with the given name and field settings.
   * @param tableName The name of the table.
   * @param field The field settings for the table.
   */
  initializeTable(tableName: string, field: FormField) {
    const validationConditions = field.ValidationCondition ? field.ValidationCondition.toLocaleLowerCase().split(',') : [];
    const isSelectable = validationConditions.includes('selectable');
    const isSelectAll = validationConditions.includes('selectall');
    let columns = Object.keys(field.Attributes);

    if (isSelectable) {
      columns.unshift('select');
      const checkboxArray = this.formBuilder.array(
        Object.keys(field.Attributes).map(() => new FormControl(false))
      );
      this.dynamicForm.addControl(tableName + "_select", checkboxArray);
    }

    this.tableColumns[tableName] = columns;
    this.loadTableData(tableName, field.DataUrl, isSelectable, isSelectAll);
  }


  /**
   * Loads table data from a specified URL.
   * @param tableName The name of the table.
   * @param tblDataUrl The URL to fetch data from.
   * @param isSelectable Optional flag to indicate if rows are selectable.
   */
  loadTableData(tableName: string, tblDataUrl: string, isSelectable: boolean = false, isAllSelected: boolean = false) {
    if (tblDataUrl) {
      this.pageSubscriptions.add(
        this.getFormData(tblDataUrl).subscribe((result: any) => {
          let tableData = result.result || [];

          // Ensure each row has a select property initialized
          if (isSelectable) {
            tableData.forEach(row => {
              // Initialize select property if it doesn't exist
              if (row.select === undefined) {
                row.select = isAllSelected;
              }
            });
          }

          this.tableDataSource[tableName] = new MatTableDataSource<any>(tableData);
        })
      );
    }
  }

  /**
   * Retrieves table attributes for display.
   * @param field The field containing table attributes.
   * @returns An array of key-value pairs representing table attributes.
   */
  getTableAttributes(field: FormField): { key: string; value: string }[] {
    return Object.entries(field.Attributes).map(([key, value]) => ({ key, value: String(value) }));
  }

  /**
   * Checks if all rows in a table are selected.
   * @param formName The name of the form/table to check.
   * @returns A boolean indicating if all rows are selected.
   */
  isAllSelected(formName: any) {
    if (!this.tableDataSource[formName]?.data) {
      return false;
    }
    const numSelected = this.tableDataSource[formName].data.filter(row => row.select)?.length || 0;
    const numRows = this.tableDataSource[formName].data.length;
    return numSelected === numRows;
  }

  /**
   * Selects or deselects all rows in a table.
   * @param formName The name of the form/table.
   * @param isAllSelected Boolean indicating if all should be selected or deselected.
   */
  selectAll(formName: string, isAllSelected: boolean) {
    if (isAllSelected) {
      // Use forEach instead of map for side effects
      this.tableDataSource[formName].data.forEach(row => {
        row.select = true;
      });
    } else {
      this.tableDataSource[formName].data.forEach(row => {
        row.select = false;
      });
    }
  }

  /**
   * Clears all selections in a table.
   * @param formName The name of the form/table.
   */
  clearSelection(formName: string) {
    // Use forEach with proper assignment instead of expression assignment
    this.tableDataSource[formName].data.forEach(row => {
      row.select = false;
    });
  }

  /**
   * Handles row selection in a table.
   * @param row The row to toggle selection for.
   */
  onSelectRow(row: any) {
    // Toggle the select property
    row.select = !row.select;
  }

  /**
   * Submits the form data to the server.
   */
  submitForms(submitAllForms: boolean = false) {
    // Get the modified forms by checking if any field in the snapshot differs from the current value
  this.formsStatus.forEach((formStatus) => {
    const formIndex = +formStatus.formId; // Get form index from formId (assuming formId is the index as string)
    const form = this.forms[formIndex];

    // Initialize form as unmodified
    let isModified = false;
    let currentControlValue;
    // Check each field in the form to see if its current value differs from the snapshot
    form.Fields.forEach(field => {
      currentControlValue = this.dynamicForm.get(field.FieldName)?.value;
      // Compare current value with the snapshot value
      if (field.FieldType === "table")
      {
        isModified = true; // Mark the form as modified if a field value differs

        // Check if this is a selectable table
        const validationConditions = field.ValidationCondition ? field.ValidationCondition.toLowerCase().split(',') : [];
        const isSelectable = validationConditions.includes('selectable');

        if (isSelectable) {
          // Only include selected rows in the submission
          if (!this.tableDataSource[field.FieldName]) {
            field.SelectedValue = JSON.stringify([]);
          } else {
            const selectedRows = this.tableDataSource[field.FieldName].data.filter(row => row.select === true);
            // Remove the 'select' field from each row before sending to API
            const cleanedSelectedRows = selectedRows.map(row => {
              const { select, ...cleanRow } = row;
              return cleanRow;
            });

            field.SelectedValue = JSON.stringify(cleanedSelectedRows);

          }
        } else {
          // For non-selectable tables, include all rows
          if (!this.tableDataSource[field.FieldName]) {
            field.SelectedValue = JSON.stringify([]);
          } else {
            // Remove the 'select' field from each row before sending to API (in case it exists)
            const cleanedRows = this.tableDataSource[field.FieldName].data.map(row => {
              const { select, ...cleanRow } = row;
              return cleanRow;
            });
            field.SelectedValue = JSON.stringify(cleanedRows);
          }
        }
      }
      if (Array.isArray(this.snapshotDynamicFormFields[field.FieldName]) && Array.isArray(currentControlValue)) {
        // Compare arrays element by element
        if (!this.areArraysEqual(this.snapshotDynamicFormFields[field.FieldName] as any, currentControlValue)) {
          isModified = true; // Mark the form as modified if a field value differs
        }
      } else {
        // For non-array values, compare directly
        if (this.snapshotDynamicFormFields[field.FieldName] != currentControlValue) {
          isModified = true; // Mark the form as modified if a field value differs
        }
      }

    });

    // Update the form's status based on whether it's modified
    // If submitAllForms is true, mark all forms as modified to force submission
    // If this is the first submission (!this.initialSubmit), mark all forms as modified
    if (submitAllForms) {
      // When explicitly requesting to submit all forms, mark this form as modified
      formStatus.formStatus = 'modified';
    } else if (!this.initialSubmit) {
      // On first submission, mark all forms as modified
      formStatus.formStatus = 'modified';
    } else {
      // Otherwise, only mark as modified if actually modified
      formStatus.formStatus = isModified ? 'modified' : 'unchanged';
    }

  });

     // Get the modified forms only
    const modifiedFormsStatus = this.formsStatus.filter(status => status.formStatus === 'modified');

    if (modifiedFormsStatus.length === 0) {
      // If no forms are modified, exit early
      return; // Exit early if no forms are modified
    }

    const requests = modifiedFormsStatus.map((formStatus) => {
      const formIndex = +formStatus.formId; // Get form index from formId (assuming formId is the index as string)
      const form = this.forms[formIndex];


      form.Fields = this.UpdateJsonLoad(form.Fields); // Update fields with values
      let formCopy = JSON.parse(JSON.stringify(form));
      formCopy.Fields.map(field => field.FieldName = this.splitFormIndex(field.FieldName));

      this.Rules[formIndex].JsonData = JSON.stringify(formCopy); // Update rule JSON with modified form

      this.saveFormSnapshot();
      this.updateFormStatus(formIndex.toString(), "Submitted");

      const emptyParamMap: ParamMap = convertToParamMap({})
      this.initialSubmit = true;
      // Submit only the modified form
      return this.FormService.ExecuteWorkFlow(
        this.siteId,
        this.workFlowProcess.workFlowId,
        this.workFlowProcess.workFlowProcessId,
        this.Rules[formIndex],
        emptyParamMap
      );
    });

    // Use forkJoin to wait for all requests to complete
  this.pageSubscriptions.add(
    forkJoin(requests).pipe(
      tap(responses => {
        const newResp: any[] = [];
        responses.forEach(response => {
          newResp.push(response);

          /* *** If we want to log each response ***
          if (response != null && response.IsSuccessful) {
            console.log("Form submitted successfully!", response);
          } else {
            console.log("Form submission failed!", response);
          }
          */
        });

        // Load data source after all responses have returned
        this.dataSource.data = this.buildFileTree(newResp, 0);
      }),
      catchError(error => {
        throw error; // Re-throw the error if necessary
      })
    ).subscribe()
  );

  //Will Be Removed
  //this.submitted = showSubmit;

  }

  /**
   * Placeholder for creating a PDF from form data.
   */
  createPdfs() {

     // Ensure PdfFillerRules is available and has data
  if (this.PdfFillerRules != null && this.PdfFillerRules.length > 0) {

    const iframeInit = this.epividianCommon.readFromStorage("iframeInit");

    // Initialize PDF loading status array
    this.pdfLoadedStatus = new Array(this.PdfFillerRules.length).fill(false);

    // Clear any existing timeouts
    if (this.pdfLoadTimeouts.length > 0) {
      this.pdfLoadTimeouts.forEach(timeout => {
        if (timeout) clearTimeout(timeout);
      });
      this.pdfLoadTimeouts = [];
    }

    if (iframeInit !== "true") {
    setTimeout(() => {
      this.renderPdfSection.fill(false);
      this.formExpandedState[0] = false;
      setTimeout(() => {
        this.formExpandedState[0] = true;
          this.renderPdfSection[0] = true;
      }, 1000);
    }, 1000);


  }

    // Use setTimeout to delay execution, giving Angular time to finalize changes

        this.pdfButtonLabel = "Update PDF"; // Change the button label to indicate progress
        let tmpUrl = this.userContext.apihandler.envUrlFromType(ApiTypes.PdfFiller);
        let pdfRoute = ApiRoutes.PdfFillerViewer.toString();
        let pdfRuleId = '0';
        for(let pdfId=0; pdfId < this.PdfFillerRules.length; pdfId++)
        {
          let pdfRoute = ApiRoutes.PdfFillerViewer.toString();
          this.PdfFillerRules.length>0 ? pdfRuleId = this.PdfFillerRules[pdfId].RuleID.toString() : pdfRuleId = '0';

          //Will need to find a better way to map transforms to steps right now only 1 transform is expected.
          if (this.writeFileToWorkflowRule[pdfId].RuleID && !isNaN(this.writeFileToWorkflowRule[pdfId].RuleID) && this.writeFileToWorkflowRule[pdfId].RuleID > 0)
          {
            pdfRuleId = `${pdfRuleId}|${this.writeFileToWorkflowRule[pdfId].RuleID.toString()}|${this.workFlowId.toString()}`;
          }

          pdfRoute = pdfRoute.replace('{{siteId}}', this.userContext.siteId.toString())
                                  .replace('{{workFlowProccessId}}', this.WorkFlowProcessId)
                                  .replace('{{formRuleId}}', pdfRuleId);

          if (this.isHistory)
          {
            pdfRoute = pdfRoute + "?IsHistory=true";
          }
          this.pdfFillerUrl[pdfId] =  this.sanitizer.bypassSecurityTrustResourceUrl(tmpUrl + pdfRoute);
        }

    }
    // Fallback: if iframe(s) haven't fired the load event after 3 seconds, try reloading them
    const fallbackRefresh = () => {
      if (!this.isPdfLoaded) {
        for (let i = 0; i < this.PdfFillerRules.length; i++) {
          const iframe = document.getElementById(`pdfFiller_${i}`) as HTMLIFrameElement;
          if (iframe && iframe.contentWindow) {
            // Use reload() to trigger a fresh load instead of reassigning src
            try {
              iframe.contentWindow.location.reload();
            } catch (e) {
              // Fallback if reload fails, reassign src as last resort
              iframe.src = (this.pdfFillerUrl[i] as any).changingThisBreaksApplicationSecurity;
            }
          }

          // Mark PDF as loaded after a maximum wait time in history mode
          if (this.isHistory && !this.pdfLoadedStatus[i]) {
            // Set a timeout to mark the PDF as loaded after 15 seconds maximum
            this.pdfLoadTimeouts[i] = setTimeout(() => {
              this.pdfLoadedStatus[i] = true;
              this.layoutService.hideSpinner();
            }, 15000);
          }
        }

        // Optionally, set another fallback timer if still not loaded
        setTimeout(() => {
          if (!this.isPdfLoaded) {
            // Force hide spinner after multiple attempts
            this.layoutService.hideSpinner();
            // Mark all PDFs as loaded to hide loading indicators
            if (this.isHistory) {
              this.pdfLoadedStatus.fill(true);
            }
          }
        }, 20000);
      }
    };

    // Set a final timeout that forces all PDFs to be marked as loaded after 30 seconds
    if (this.isHistory) {
      setTimeout(() => {
        const stillLoading = this.pdfLoadedStatus.some(status => status === false);
        if (stillLoading) {
          this.pdfLoadedStatus.fill(true);
          this.layoutService.hideSpinner();
        }
      }, 30000); // 30 seconds timeout
    }

    setTimeout(fallbackRefresh, 5000);
  }


  /**
   * Updates the form fields with JSON load.
   * @param userForm An array of form fields to update.
   * @returns The updated form fields.
   */
  public UpdateJsonLoad(userForm: FormField[]) {
    userForm.forEach(field => {
      // Skip table fields as they are handled separately in submitForms
      if (field.FieldType === "table") {
        return; // Skip this field
      }

      // Always update the field's SelectedValue with the current form control value
      // regardless of whether it already has a value or not
      if (field.FieldType === "checkbox") {
        field.SelectedValue = JSON.stringify(this.dynamicForm.get(field.FieldName)?.value);
      } else {
        field.SelectedValue = this.dynamicForm.get(field.FieldName)?.value;
      }
    });
    return userForm;
  }

  //#endregion


  //#region Tree View Methods

  private transformer = (node: JsonNode, level: number) => {
    return {
      expandable: !!node.children && node.children.length > 0,
      name: node.name,
      value: node.value,
      level: level,
    };
  };

  treeControl = new FlatTreeControl<FlatNode>(
    node => node.level,
    node => node.expandable
  );

  treeFlattener = new MatTreeFlattener<JsonNode, FlatNode>(
    this.transformer,
    node => node.level,
    node => node.expandable,
    node => node.children
  );

  dataSource = new MatTreeFlatDataSource(this.treeControl, this.treeFlattener);

  buildFileTree(obj: any, level: number): JsonNode[] {
    return Object.keys(obj).map(key => {
      const value = obj[key];
      const node: JsonNode = {
        name: key
      };

      if (typeof value === 'object' && value !== null) {
        node.children = this.buildFileTree(value, level + 1);
      } else {
        node.value = value;
      }

      return node;
    });
  }

  hasChild = (_: number, node: FlatNode) => node.expandable;

  //#endregion



// Optionally, add methods to change the step
nextStep() {
  this.trackerPositionIndex++;
}

previousStep() {
  this.trackerPositionIndex--;
}

getRemainingFields(form): number {
  return form.Fields.filter((field) => field.isRequired && !this.dynamicForm.controls[field.FieldName].valid).length;
}

allFieldsValid(form): boolean {
  return form.Fields.every((field) => !field.isRequired || this.dynamicForm.controls[field.FieldName].valid);
}

extractVariables(inputString: string): string[] {
  // Regular expression to match anything between { and }
  const regex = /\{([^}]+)\}/g;

  // Array to store the found variables
  let match;
  const variables: string[] = [];

  // Loop through the matches and extract variable names
  while ((match = regex.exec(inputString)) !== null) {
    // Add the found variable (without the curly braces) to the array
    variables.push(match[1]);
  }

  return variables;
}

// Method to check and update WorkFlowDescription
updateWorkFlowDescription(fieldName: string, replacementValue: string): void {
  // Check if WorkFlowDescription is an empty string
  if (this.WorkFlowDescription === "") {
    // If empty, set it to workFlowTextCode
    this.WorkFlowDescription = this.workFlowProcess.workFlowTextCode;
  } else {
    // If not empty, replace the fieldName with the replacementValue
    const regex = new RegExp(`{${fieldName}}`, 'g'); // Create a regex to find the fieldName wrapped in curly braces
    this.WorkFlowDescription = this.WorkFlowDescription.replace(regex, replacementValue);
  }
}


stripDate(input: string): string | null {
  // Use a regular expression to match the date portion (MM/DD/YYYY format)
  const datePattern = /\b\d{1,2}\/\d{1,2}\/\d{4}\b/;
  const match = input.match(datePattern);

  if (match) {
    // If a date is found, split it into parts
    const [month, day, year] = match[0].split('/');
    // Return in YYYY-MM-DD format
    return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
  }

  // Return null if no valid date is found
  return null;
}

containsPlaceholder(description: string): boolean {
  return description.includes('__') && description.includes('{') && description.includes('}');
}


lookupCheckboxValue(checkboxArray: FormArray, value: any): number | null {
  // Iterate through the checkbox array controls
  for (let i = 0; i < checkboxArray.length; i++) {
    // Get the control at the current index
    const control = checkboxArray.at(i);

    // If the value of the control matches the provided value, return the index
    if (control.value === value) {
      return i;
    }
  }

  // If no match is found, return null
  return null;
}

getKeyIndex(obj: { [key: string]: string }, targetValue: string): number | null {
  const value = Object.values(obj);
  const index = value.indexOf(targetValue);
  return index !== -1 ? index : null;
}
//
areArraysEqual(arr1: any[], arr2: any[]): boolean {
  if (arr1.length !== arr2.length) {
    return false;
  }
  for (let i = 0; i < arr1.length; i++) {
    if (arr1[i] !== arr2[i]) {
      return false;
    }
  }
  return true;
}

/**
 * Submits a single section of the form
 * @param sectionIndex The index of the section to submit
 */
submitSingleSection(sectionIndex: number) {
  // Find the form status for this section
  const formStatus = this.formsStatus.find(status => status.formId === sectionIndex.toString());

  if (!formStatus) {
    return; // Exit if no form status found
  }

  // Mark only this section as modified
  formStatus.formStatus = 'modified';

  // Get the form for this section
  const form = this.forms[sectionIndex];

  if (!form) {
    return; // Exit if no form found
  }

  // Update the form fields with current values
  form.Fields = this.UpdateJsonLoad(form.Fields);

  // Create a copy of the form with proper field names
  let formCopy = JSON.parse(JSON.stringify(form));
  formCopy.Fields.map(field => field.FieldName = this.splitFormIndex(field.FieldName));

  // Update the rule JSON with the modified form
  this.Rules[sectionIndex].JsonData = JSON.stringify(formCopy);

  // Save a snapshot of the current form state
  this.saveFormSnapshot();

  // Update the form status to Submitted
  this.updateFormStatus(sectionIndex.toString(), "Submitted");

  // Create an empty parameter map for the API call
  const emptyParamMap: ParamMap = convertToParamMap({});

  // Set initialSubmit to true to indicate that at least one submission has occurred
  this.initialSubmit = true;

  // Submit only this section's form
  this.pageSubscriptions.add(
    this.FormService.ExecuteWorkFlow(
      this.siteId,
      this.workFlowProcess.workFlowId,
      this.workFlowProcess.workFlowProcessId,
      this.Rules[sectionIndex],
      emptyParamMap
    ).pipe(
      catchError(error => {
        //console.error(`An error occurred during section ${sectionIndex} submission`, error);
        throw error;
      })
    ).subscribe()
  );
}

//Collapse current section and expand next section
nextSection(currentSectionIndex: number) {
  this.formExpandedState[currentSectionIndex] =  false;

  let nextSectionId = currentSectionIndex + 1;

  if (nextSectionId <= this.forms.length && this.stepper.selectedIndex === 0) {
    this.formExpandedState[nextSectionId] = true;
  }

  //1 is the index of the pdf filler section
  if (this.stepper.selectedIndex === 1) {
    // In history mode, just expand the next section without submitting
    if (this.isHistory) {
      // Just expand the next section if it exists
      if (nextSectionId < this.PdfFillerRules.length) {
        this.formExpandedState[nextSectionId] = true;
        this.renderPdfSection[nextSectionId] = true;
      }
    } else {
      // In normal mode, trigger the PDF submission
      this.triggerChildButton(currentSectionIndex, true);
    }
  }
  else { //0 is the index of the form section
    // In history mode, don't submit the form
    if (!this.isHistory) {
      // Submit only the current section when Next Section is clicked
      this.submitSingleSection(currentSectionIndex);
    }
  }
}



continueStep() {
  let currentStep = this.stepper.selectedIndex;
  if (!this.validateAllForms() && !this.isHistory)
  {
    return;
  }

  this.layoutService.showSpinner();
  this.layoutService.startTimer();

  this.signFormGroup.get('signCtrl')?.setValue('signed');

  // Set programmatic navigation flag to true before navigating
  this.programmaticNavigation = true;
  this.stepper.next();

  // Reset the flag after a short delay
  setTimeout(() => {
    this.programmaticNavigation = false;
  }, 100);

  if (this.stepper.selectedIndex != currentStep)
  {
    // Get all keys of the object
    const keys = Object.keys(this.formExpandedState);

    // Set all keys to false except the first one
    keys.forEach((key, index) => {
      this.formExpandedState[key] = index === 0; // true for the first item, false for the rest
    });
  }

  if (this.stepper.selectedIndex == 1)
  {
    // When clicking Continue, submit all forms that haven't been saved
    this.submitForms(true);
    this.createPdfs();
  }

}

submitPdfContinue() {
  this.layoutService.showSpinner();
  for(let pdfFormIds=0; pdfFormIds <= this.PdfFillerRules.length-1; pdfFormIds++)
  {
    if (this.formsSubmitted != null && this.formsSubmitted[pdfFormIds] != null && this.formsSubmitted[pdfFormIds].isSigned == true && this.formsSubmitted[pdfFormIds].submitted == true)
    {
      continue;
    }
    else{
      this.triggerChildButton(pdfFormIds)
    }
  }

      // Get all keys of the object
      const keys = Object.keys(this.formsSubmitted);

      // Set all keys to false except the first one
      let AllSignedAndSubmitted: boolean = false
      keys.forEach((key) => {
        if (this.formsSubmitted[key].isSigned == true && this.formsSubmitted[key].submitted == true)
          {
            AllSignedAndSubmitted = true;
          } else
          {
            AllSignedAndSubmitted = false;
          }
      });

  let failedMessage = "Something went wrong. We did not receive confirmation that Thera received your form. Please proceed with enrollment by printing and faxing this form to the number provided on the form"

  if (AllSignedAndSubmitted)
  {
    //Submit the form to the server before navigating to the next page..
    //If validation fails, the form will need to be adjusted before continuing
    const emptyParamMap: ParamMap = convertToParamMap({})
    this.apiResultStatus = false;
    // Submit only the modified form
    this.pageSubscriptions.add(
      this.FormService.ExecuteWorkFlow(
        this.siteId,
        this.workFlowProcess.workFlowId,
        this.workFlowProcess.workFlowProcessId,
        this.apiMapRule,
        emptyParamMap
      ).subscribe((mapResponse) => {
        if (mapResponse != null) {
          this.FormService.ExecuteWorkFlow(
            this.siteId,
            this.workFlowProcess.workFlowId,
            this.workFlowProcess.workFlowProcessId,
            this.apiRequestRules[0],
            emptyParamMap
          ).subscribe((apiRequest) => {
            if (apiRequest != null) {
              this.FormService.ExecuteWorkFlow(
                this.siteId,
                this.workFlowProcess.workFlowId,
                this.workFlowProcess.workFlowProcessId,
                this.apiRequestRules[1],
                emptyParamMap
              ).subscribe((apiRequest) => {
              //  this.spinnerService.hide();
                if (apiRequest != null) {
                  if (apiRequest.Message && apiRequest.Message.toLowerCase() == "unauthorized") {
                    this.popupMessage(failedMessage);
                  }
                  else if (apiRequest.IsSuccessful) {
                  this.leadId = this.getValueByPath(apiRequest, 'Message.leadID');
                  if (this.leadId!=undefined && this.leadId != null && this.leadId != "")
                    {
                      this.apiResultStatus = true;
                      this.stepper.next();

                      this.popupMessage("Form successfully submitted to Thera. Please Click View Form to print a copy to keep for your records.");
                    }
                    else
                    {
                      this.popupMessage(failedMessage);
                      this.stepper.next();
                    }
                  }
                } else {
                  this.popupMessage(failedMessage);
                }
                this.layoutService.hideSpinner();
              });
            } else {
              this.popupMessage(failedMessage);
              this.layoutService.hideSpinner();
            }
          });
        } else {
          this.popupMessage(failedMessage);
          this.layoutService.hideSpinner();
        }
      })
    );

  }
  else
  {
    this.layoutService.hideSpinner();
    this.popupMessage(failedMessage);
  }
}

//Colapse current section and expand next section
toggleSection(currentSectionIndex: number) {
  const newState = !this.formExpandedState[currentSectionIndex];
  this.formExpandedState[currentSectionIndex] = newState;
  this.renderPdfSection[currentSectionIndex] = newState;
}

// Method to validate the entire form
validateAllForms(): boolean {
  // Mark all controls as touched to trigger validation messages
  this.dynamicForm.markAllAsTouched();

  if (this.dynamicForm.valid) {
    this.fillFormGroup.get('fillCtrl')?.setValue('continue');
    return true;

  } else {
    this.fillFormGroup.get('fillCtrl')?.setValue('RemoveToBlankSpaceToInforceValidation');
    return false;
  }
}

// Method to validate if all PDFs are properly signed
validateAllPdfsAreSigned(): boolean {
  // Check if we have any PDF rules
  if (!this.PdfFillerRules || this.PdfFillerRules.length === 0) {
    return true; // No PDFs to sign
  }

  // Check if all PDFs are signed
  for (let i = 0; i < this.PdfFillerRules.length; i++) {
    if (!this.formsSubmitted[i] || !this.formsSubmitted[i].isSigned || !this.formsSubmitted[i].submitted) {
      return false; // At least one PDF is not properly signed
    }
  }

  return true; // All PDFs are properly signed
}

// Method to go back to the Fill step
goBackToFillStep(): void {
  // Set programmatic navigation flag to true before navigating
  this.programmaticNavigation = true;
  this.stepper.selectedIndex = 0;

  // Reset the flag after a short delay
  setTimeout(() => {
    this.programmaticNavigation = false;
  }, 100);
}

getFutureTime(minutesToAdd: number): Date {
  const now = new Date(); // Get the current date and time
  return new Date(now.getTime() + minutesToAdd * 60 * 1000); // Add minutes in milliseconds
}

// Method to trigger the Submit in the pdf filler iframe
triggerChildButton(formId: number, isSection: boolean = false) {
  this.layoutService.showSpinner();
  this.layoutService.startTimer();

  let formName = this.PdfFillerRules[formId].Name.replace(/-/g, '_').replace(/\s+/g, '_');
  const iframe = document.getElementById('pdfFiller_'+formId.toString()) as HTMLIFrameElement;
  if (isSection)
  {
    iframe.contentWindow?.postMessage({ action: `TriggerPdf**SectionSubmit-${formId}-${formName}` }, '*');
  } else
  {
    iframe.contentWindow?.postMessage({ action: `TriggerPdf**ContinueSubmit-${formId}-${formName}` }, '*');
  }
}

sendLocalStorageToChild(formId: number) {
  // Use the IframeJwtService to securely send the JWT token to the iframe
  const iframeId = 'pdfFiller_' + formId.toString();

  // Get the origin from the iframe URL for better security
  const iframe = document.getElementById(iframeId) as HTMLIFrameElement;
  if (!iframe) {
    // This shouldn't happen but does, we need to look into this.
    return;
  }

  // Use the service to send the JWT token
  this.iframeJwtService.sendJwtToIframe(iframeId);
}

/**
 * Handles the iframe load event for PDFs
 * @param formId The index of the PDF form that was loaded
 */
onPdfIframeLoad(formId: number) {
  // Send JWT token to the iframe using our secure service
  this.sendLocalStorageToChild(formId);

  // Set up token refresh handling for this iframe
  const iframeId = `pdfFiller_${formId}`;
  this.iframeJwtService.setupTokenRefreshHandling([iframeId]);

  // Set up a timeout to mark the PDF as loaded after a maximum wait time
  if (this.isHistory) {
    // If we're currently downloading a PDF, don't set the loaded status yet
    // The PdfLoaded message will handle that
    if (this.pdfDownloading) {
      return;
    }

    // Clear any existing timeout for this form
    if (this.pdfLoadTimeouts[formId]) {
      clearTimeout(this.pdfLoadTimeouts[formId]);
    }

    // Initialize the PDF loaded status array if needed
    if (this.pdfLoadedStatus[formId] === undefined) {
      this.pdfLoadedStatus[formId] = false;
    }

    // Mark this PDF as loaded immediately if it's the first load
    if (!this.pdfLoadedStatus[formId]) {
      this.pdfLoadedStatus[formId] = true;

      // Check if all PDFs are loaded
      const allPdfsLoaded = this.pdfLoadedStatus.every(status => status === true);
      if (allPdfsLoaded) {
        this.layoutService.hideSpinner();
      }
    }

    // Set a backup timeout to mark the PDF as loaded after 10 seconds if no 'PdfLoaded' message is received
    this.pdfLoadTimeouts[formId] = setTimeout(() => {
      if (!this.pdfLoadedStatus[formId]) {
        this.pdfLoadedStatus[formId] = true;

        // Check if all PDFs are loaded
        const allPdfsLoaded = this.pdfLoadedStatus.every(status => status === true);
        if (allPdfsLoaded) {
          this.layoutService.hideSpinner();
        }
      }
    }, 10000); // 10 seconds timeout
  } else {
    // For non-history mode, make sure spinner is hidden after iframe loads
    this.layoutService.hideSpinner();
  }
}


routeToDashboard() {
  this.router.navigate(['/Dashboard']);
}

/**
 * Navigate back to the Forms step (step 0) in history mode
 */
backToFormsStep() {
  // Set programmatic navigation flag to true before navigating
  this.programmaticNavigation = true;

  // Navigate to step 0 (Forms step)
  this.stepper.selectedIndex = 0;

  // Reset the flag after a short delay
  setTimeout(() => {
    this.programmaticNavigation = false;
  }, 100);

}



public popupMessage(data: any) {
  this.openDialog = this.dialog.open(DialogResponseComponent, {
    data: data, width: '400px', height: 'auto'
  });
}


ngOnDestroy(): void {
  // Complete the destroyed$ subject to clean up subscriptions
  this.pageSubscriptions.unsubscribe();
}

getValueByPath(obj: any, path: string): any {
  const keys = path.split('.');
  let current = obj;

  for (let i = 0; i < keys.length; i++) {
    const key = keys[i];

    // If the current value is a string and we still have more keys to go,
    // attempt to parse it as JSON.
    if (typeof current === 'string' && i < keys.length) {
      try {
        current = JSON.parse(current);
      } catch (e) {
        return undefined; // or handle error as needed
      }
    }

    if (current && current.hasOwnProperty(key)) {
      current = current[key];
    } else {
      return undefined;
    }
  }

  return current;
}


}

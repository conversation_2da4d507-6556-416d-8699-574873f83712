import { Component, EventEmitter, Inject, Input, Output, output, SimpleChanges } from '@angular/core';
import { detailsTableService } from './Services/details-table.service';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { HuddleDetail } from '../models/huddle-detail.model';
import { NgxSpinnerService } from 'ngx-spinner';
import { Router } from '@angular/router';
import { ILocation } from '../models/location.model';
import { ApiRoutes } from 'src/app/shared-services/ep-api-handler/api-option-enums';

@Component({
  selector: 'epividian-details-table',
  templateUrl: './details-table.component.html',
  styleUrl: './details-table.component.scss'
})
export class DetailsTableComponent {

  @Input() selectedDate: Date = new Date();
  @Input() selectedProvider: string = "";
  @Input() selectedLocation: ILocation = {} as ILocation;
  @Input() selectedFilter: string = "All Appointments"
  @Output() huddleAppointments: EventEmitter<HuddleDetail[]> = new EventEmitter<HuddleDetail[]>();

  public siteId: string = "";
  public detailData: HuddleDetail[] = [];
  public numberOfAppointments: number = 0;
  public numberOfPatients: number = 0;

  constructor(
    public detailsService: detailsTableService,
    private userContext: UserContext,
    private router: Router,
    @Inject(NgxSpinnerService) private spinnerService: NgxSpinnerService
  ) {}

  ngOnInit(){
    this.siteId = this.userContext.GetCurrentSiteValue().toString();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (this.siteId != ""){
      if (changes['selectedDate']) {
        this.selectedFilter = "All Appointments";
        this.getDetails();
      }
      if (changes['selectedProvider']) {
        this.selectedFilter = "All Appointments";
        this.getDetails();
      }
      if (changes['selectedLocation']) {
        this.selectedFilter = "All Appointments";
        this.getDetails();
      }
      if (changes['selectedFilter']){
        this.getDetails();
      }
    }
  }

  getDetails(){
    this.spinnerService.show();
    this.detailsService.getHuddleDetails(this.siteId,this.selectedDate, this.selectedFilter, this.selectedProvider, this.selectedLocation.id).subscribe(res => {
      this.detailData = res;
      this.detailData.forEach(appointment => {
        appointment.appointmentType = this.getAppointmentType(appointment.appointmentType)
        appointment.FlowSheetImage = this.getFlowSheetImage(appointment.hivFlag, appointment.hcvFlag)
        appointment.mrn = this.extractMRN(appointment.patientName);
      });
      this.numberOfAppointments = this.detailData.length;
      this.numberOfPatients = this.getUniquePatients();
      if(this.selectedFilter == "All Appointments"){
        this.emitHuddleAppointments();
      }
      this.spinnerService.hide();
    });
  }

  getAppointmentsWithFlagSet(flagPosition: number): number {
    return this.detailData.filter(appointment => this.detailsService.isFlagSet(appointment, flagPosition)).length;
  }

  getDetailsList(appointment: HuddleDetail){
    let details: string[] = [];
    details = appointment.details;
    if(!appointment.showAllDetails){
      if (appointment.details.length > 3){
        details = details.slice(0,3);
      }
    }
    return details;
  }

  setShowAlldetails(appointment: HuddleDetail, showAll: boolean){
    appointment.showAllDetails = showAll;
  }

  setShowHideAll(showAll: boolean){
    this.detailData.forEach(appt => appt.showAllDetails = showAll);
  }

  getStatus(status: string, appointmentType: string){
    if (appointmentType == "Telehealth" && !this.isNoShowOrCanceled(status)){
      return "telehealth";
    }
    return status.replace(/[\s&-]+/g, '').toLowerCase();
  }

  isNoShowOrCanceled(statusDesc: string){
    if(statusDesc == 'No Show'){
      return true;
    }
    if (statusDesc.includes("Canceled")){
      return true;
    }

    return false;
  }

  getAppointmentType(appointmentType: string){
    if (appointmentType != null){
      if(appointmentType.length > 22){
        return appointmentType.slice(0,22) + "...";
      }
      else
      {
        return appointmentType;
      }
    }
    else
    {
      return "";
    }
  }

  emitHuddleAppointments() {
    this.huddleAppointments.emit(this.detailData);
  }

  showRecordsQualityGapReport(aptRowData: HuddleDetail){
      let demographicId = `[${aptRowData.demographicsId}]`
      demographicId = encodeURIComponent(demographicId);
      const url = ApiRoutes.QualityGapReport
            .replace('{{siteId}}', this.siteId)
            .replace('{{demographicId}}', demographicId);
      this.router.navigateByUrl(url);
  }

  showFlowSheetHIVReport(aptRowData: HuddleDetail){
      let demographicId = `[${aptRowData.demographicsId}]`
      demographicId = encodeURIComponent(demographicId);
      const url = ApiRoutes.PatientFlowsheetReport
            .replace('{{siteId}}', this.siteId)
            .replace('{{demographicId}}', demographicId);
      this.router.navigateByUrl(url);
  }

  getFlowSheetImage(hiv: number, hcv: number) {
    if (hiv >= 1 && hcv == 0) return '../../../../assets/images/hiv.png';
    else if (hiv == 0 && hcv >= 1) return '../../../../assets/images/hcv.png';
    else if (hiv >= 1 && hcv >= 1)
      return '../../../../assets/images/hivhcv.png';
    //if (hiv == 0 && hcv == 0)
    else return '';
  }

  extractMRN(patientName: string): number {
    let mrn = -1;
    const regex = /\((\d+)\)/; // Regular expression to find digits inside parentheses
    const match = patientName.match(regex);

    if (match && match[1]) {
      mrn = parseInt(match[1], 10); // Convert the captured string to an integer
    }
    return mrn
  }

  getUniquePatients(){
    const uniqueDemographicIds = new Set<number>(this.detailData.map(item => item.demographicsId));
    return uniqueDemographicIds.size;
  }
}

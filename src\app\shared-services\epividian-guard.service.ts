import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivate, Router, RouterStateSnapshot } from '@angular/router';
import { EpividianCommon } from '../modules/utility/EpividianCommon';
import { RouteCacheService } from './route-cache.service';

@Injectable({
  providedIn: 'root'
})
export class EpividianGuardService implements CanActivate {

  constructor(
    private router: Router,
    private epividianCommon: EpividianCommon,
    private routeCacheService: RouteCacheService
  ) { }

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean {
    // Check if user has a valid JWT session
    const validSession = this.epividianCommon.LoadValidSessionObj();

    if (validSession) {
      // User is authenticated, allow access
      console.log('User authenticated, allowing access to:', state.url);
      return true;
    }

    // User is not authenticated
    console.log('User not authenticated, caching route and redirecting to login:', state.url);

    // Cache the intended route for post-login redirect
    this.routeCacheService.cacheRoute(state.url);

    // Redirect to login page
    this.router.navigate(['/Auth/Login']);
    return false;
  }

}

import { Component, EventEmitter, Input, input, Output, SimpleChanges, ViewChild  } from '@angular/core';
import {MatTooltipModule} from '@angular/material/tooltip';
import { HuddleDetail } from '../models/huddle-detail.model';
import { HuddleFilterService } from './Services/huddle-filter.service';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { HuddleFilter } from '../models/huddle-filter.model';

@Component({
  selector: 'epividian-huddle-filter',
  templateUrl: './huddle-filter.component.html',
  styleUrl: './huddle-filter.component.scss'
})
export class HuddleFilterComponent {

  @Output() filterSelected: EventEmitter<string> = new EventEmitter<string>();
  @Input() huddleAppointments: HuddleDetail[] = [];

  public selectedFilter: string = "All Appointments";
  public selectedIndex: number = 0;
  public filters: HuddleFilter[] = [];
  public siteId: string = "";

  tooltipMessage = 'Additional information goes here.';
  isTooltipDisabled = true;

  constructor(public filterService: HuddleFilterService, private userContext: UserContext) {}

  ngOnInit(){
    this.siteId = this.userContext.GetCurrentSiteValue().toString();
    this.getFilters(this.siteId);
    this.emitSelectedFilter();
  }

  getFilters(siteId: string){
    this.filterService.GetFilters(siteId).subscribe( res => {
        this.filters = res;
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['huddleAppointments']) {
      this.selectFilter(0);
    }
  }

  // Get number of unique patients with appointments that match that filter
  getAppointmentsWithFlagSet(flagPosition: number): number {
    const numberOfUniquePatients = new Set(
      this.huddleAppointments
        .filter(appointment => this.isFlagSet(appointment, flagPosition))
        .map(appointment => appointment.demographicsId)
      );
    return numberOfUniquePatients.size;
  }

  isFlagSet(huddleDetail: HuddleDetail, flagPosition: number): boolean {
    // Ensure that the binary string exists and is long enough to check the flag position
    if (huddleDetail.huddleFlags && huddleDetail.huddleFlags.length > flagPosition) {
      return huddleDetail.huddleFlags[flagPosition] === '1';
    }
    return false;
  }

  selectFilter(selectedIndex: number) {
    this.selectedIndex = selectedIndex;
    this.selectedFilter = this.filters[selectedIndex].name;
    this.emitSelectedFilter();
  }

  getFilterToolTip(filter: string){
    switch (filter) {
      case "All Appointments":
        return("");

      case "Targeted quality gaps":
        return("Targeted quality gaps: Gaps designated by your organization for special attention");

      case "Annuals needed":
        return("Annuals Needed: No physicals in the last year");

      case "New Patients":
        return("New patients: No prior visit or have a prior visit >3 years old");

      case "Vaccines & injections":
        return("Vaccines & Injections: Missing from record and indicated as needed");

      case "Heavy comorbidities":
        return("Heavy Co-morbidities: HIV+ and ASCVD > 20%, FIB-4 > 3.25, CKD-EPI < 60mL/min, or VACS index ≥50");

      case "Recent hospitalizations":
        return("Recent Hospitalizations: Record of hospitalization since last visit");

      case "No-show risk":
        return("No-show risk: At risk for loss to follow-up");

      default:
        return "No action defined for filter: " + filter;
    }
  }

  emitSelectedFilter() {
    this.filterSelected.emit(this.selectedFilter);
  }
}
  


import { Component, Inject, OnInit, AfterViewInit, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { QualityMeasuresService } from './services/quality-measures.service';
import { QualityMeasure } from '../../../shared-services/quality-measures/model/quality-measures-model';
import { NgxSpinnerService } from 'ngx-spinner';
import { TabulatorFull as Tabulator } from 'tabulator-tables';
import { jsPDF } from 'jspdf';
import { applyPlugin } from 'jspdf-autotable';
import * as XLSX from 'xlsx';
import { Chart, PieController, ArcElement, Tooltip, Legend } from 'chart.js';
import { MatIconModule } from '@angular/material/icon';
import { QualityMeasureFilters, QualityMeasureFiltersComponent } from './quality-measure-filters/quality-measure-filters.component';
import { LayoutService } from '../../shared-modules/layouts/services/layout/layout.service';
import { ActivatedRoute } from '@angular/router';
import { ReportNavWrapperComponent } from './report-nav-wrapper/report-nav-wrapper.component';

@Component({
  selector: 'app-quality-measures',
  templateUrl: './quality-measures.component.html',
  styleUrl: './quality-measures.component.css',
  imports: [
    CommonModule,
    MatIconModule,
    QualityMeasureFiltersComponent,
    ReportNavWrapperComponent
  ],
  standalone: true,
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class QualityMeasuresComponent implements OnInit, AfterViewInit {
  qualityMeasures: QualityMeasure[] = [];
  table!: Tabulator;
  private resizeObserver!: any;
  columnWidths: number[] = []; // External variable to store column widths
  groupByFields: number[] = []; // groupBy fields
  toggleGrouping: boolean = false; // Flag to track grouping state

  // Store the current filters
  currentFilters: QualityMeasureFilters | null = null;

  menuOpen = false;

  toggleMenu() {
    this.menuOpen = !this.menuOpen;
  }

  // Optional: close menu when clicking outside
  onClickOutsideMenu(event: MouseEvent) {
    const menu = document.querySelector('.dropdown');
    if (menu && !menu.contains(event.target as Node)) {
      this.menuOpen = false;
    }
  }

  constructor(
    private qualityMeasuresService: QualityMeasuresService,
    @Inject(NgxSpinnerService) private spinnerService: NgxSpinnerService,
    private layoutService: LayoutService,
    private route: ActivatedRoute
  ) {
    // Force the layout service to initialize
    console.log('Quality Measures component constructor');

    // Try to get the site ID from the URL directly
    const urlParts = window.location.pathname.split('/');
    const qualityMeasuresIndex = urlParts.indexOf('QualityMeasures');

    if (qualityMeasuresIndex !== -1 && qualityMeasuresIndex + 1 < urlParts.length) {
      const siteId = urlParts[qualityMeasuresIndex + 1];
      if (siteId && !isNaN(Number(siteId))) {
        console.log('Constructor: Found site ID in URL:', siteId);
        this.layoutService.setNavandReload(Number(siteId));
      }
    }
  }

  // Track subscriptions to clean up on destroy
  private subscriptions: any[] = [];

  ngOnInit(): void {
    // Register Chart.js components
    this.registerChartComponents();

    // Handle initial route parameters
    this.initializeFromRouteParams();

    // Subscribe to route parameter changes
    this.subscriptions.push(
      this.route.params.subscribe(params => {
        console.log('Route params changed:', params);
        this.handleSiteIdFromParams(params);
      })
    );

    // Load quality measures data
    this.loadQualityMeasuresData();
  }

  private initializeFromRouteParams(): void {
    console.log('Route params:', this.route.snapshot.params);
    console.log('Route URL:', this.route.snapshot.url);

    // Try both params and paramMap to ensure we get the siteId
    const siteIdFromParams = this.route.snapshot.params['siteId'];
    const siteIdFromParamMap = this.route.snapshot.paramMap.get('siteId');

    console.log('Site ID from params:', siteIdFromParams);
    console.log('Site ID from paramMap:', siteIdFromParamMap);

    // Use whichever method provides the siteId
    const siteId = siteIdFromParams || siteIdFromParamMap;

    if (siteId) {
      this.handleSiteId(siteId);
    } else {
      this.extractSiteIdFromUrl();
    }
  }

  private handleSiteIdFromParams(params: any): void {
    const siteId = params['siteId'];
    if (siteId) {
      this.handleSiteId(siteId);
    }
  }

  private handleSiteId(siteId: string | number): void {
    console.log('Setting site ID for report navigation:', siteId);
    // Initialize the report navigation with the site ID
    this.layoutService.setNavandReload(Number(siteId));
  }

  private extractSiteIdFromUrl(): void {
    console.warn('No site ID found in route parameters, trying to get from URL');

    // Fallback: Try to extract siteId from URL
    const urlParts = window.location.pathname.split('/');
    const qualityMeasuresIndex = urlParts.indexOf('QualityMeasures');

    if (qualityMeasuresIndex !== -1 && qualityMeasuresIndex + 1 < urlParts.length) {
      const potentialSiteId = urlParts[qualityMeasuresIndex + 1];

      if (potentialSiteId && !isNaN(Number(potentialSiteId))) {
        console.log('Found site ID in URL:', potentialSiteId);
        this.handleSiteId(potentialSiteId);
      } else {
        console.error('Could not determine site ID from URL');
      }
    } else {
      console.error('QualityMeasures not found in URL or no site ID follows it');
    }
  }

  private loadQualityMeasuresData(): void {
    // Load quality measures data
    this.spinnerService.show();

    const subscription = this.qualityMeasuresService.getMeasures().subscribe({
      next: (data) => {
        this.qualityMeasures = data.map(measure => ({
          ...measure,
          totalMeasures: measure.measuresSatisfied + measure.measuresUnsatisfied,
          satisfiedPercentage: Math.round((measure.measuresSatisfied / (measure.measuresSatisfied + measure.measuresUnsatisfied)) * 100),
          unsatisfiedPercentage: Math.round((measure.measuresUnsatisfied / (measure.measuresSatisfied + measure.measuresUnsatisfied)) * 100)
        }));
        this.spinnerService.hide();

        // Initialize table after data is loaded
        setTimeout(() => this.initializeTable(), 0);
      },
      error: (error) => {
        console.error('Error loading quality measures data:', error);
        this.spinnerService.hide();
      }
    });

    this.subscriptions.push(subscription);

    // Initialize PDF plugin
    applyPlugin(jsPDF);

    // Set up resize observer for responsive table
    this.resizeObserver = () => {
      if (this.table) {
        this.table.redraw(true); // Trigger full rerender
      }
    };
    window.addEventListener('resize', this.resizeObserver);
  }

  ngOnDestroy(): void {
    // Remove the event listener when the component is destroyed
    window.removeEventListener('resize', this.resizeObserver);

    // Destroy Tabulator instance if it exists
    if (this.table) {
      this.table.destroy();
    }

    // Clean up all subscriptions
    this.subscriptions.forEach(subscription => {
      if (subscription && typeof subscription.unsubscribe === 'function') {
        subscription.unsubscribe();
      }
    });
    this.subscriptions = [];
  }

  ngAfterViewInit(): void {
    this.initializeTable();
  }

  initializeTable(): void {
    this.table = new Tabulator("#quality-measures-table", {
      dependencies: {
        XLSX: XLSX,
        jspdf: jsPDF
      },
      downloadConfig: {
        rowGroups: false, //disable row groups in download
      },
      data: this.qualityMeasures,
      printAsHtml: true, //enable html table printing
      printStyled: true, //copy Tabulator styling to HTML table
      groupBy: ["qualityName", "groupName", "locationName"],
      groupStartOpen: [true, true, true, true],
      groupToggleElement: "header", //toggle group on click anywhere in the group header
      headerSortElement: "<i class='material-icons'>arrow_drop_up</i>", //material icon for sorting
      rowHeight: 30,
      columnDefaults:{
        resizable: false,
        headerSort: false,
      },
      columnHeaderVertAlign: "middle",
      columns: [
        { title: "Quality Name", field: "qualityName", visible: false, download: true }, //these 3 columns are hidden in the table but included in the download
        { title: "Group Name", field: "groupName", visible: false, download: true },
        { title: "Location Name", field: "locationName", visible: false, download: true },

        {
          download: false,
          field: "filter", //a dummy field to allow the .getField() method to work
          title: "<span style='padding-right: 40px;'>Measure</span>Location",
          hozAlign: "center",
          width: 240,
        },

        {
          title: "Provider",
          field: "providerName",
          width: 120
        },
        {
          title: "Trend",
          field: "trend",
          width: 60,
          cssClass: "whiteBorder",
          hozAlign: "center",
          headerHozAlign: "center",
          headerSort: false,
          formatter: (cell: any) => {
            const value = cell.getValue();
            if (value === -1) {
              return `<i class="material-icons" style="font-size: 20px; color: #808080;">south_east</i>`;
            } else if (value === 0) {
              return `<i class="material-icons" style="font-size: 20px; color: #29abe2;">arrow_forward</i>`;
            } else if (value === 1) {
              return `<i class="material-icons" style="font-size: 20px; color: #0071bc;">north_east</i>`;
            }
            return "";
          }
        },
        { title: "Qualifies", field: "totalMeasures", hozAlign: "center", headerHozAlign: "center", width: 92 },
        {
          title: "<div style='text-align: right'><div class='svg-icon satisfied-icon'></div></div>",
          titleDownload: "Measures Satisfied",
          field: "measuresSatisfied",
          hozAlign: "right",
          headerSort: false,
          width: 40,
          cssClass: "lightBlue"
        },
        {
          title: "Measures Satisfied",
          titleDownload: "%",
          field: "satisfiedPercentage",
          hozAlign: "center",
          headerWordWrap: true,
          width: 85,
          cssClass: "lightBlue",
          formatter: (cell: any) => `(${cell.getValue()}%)`
        },
        {
          title: "",
          field: "pieChart",
          headerSort: false,
          download: false,
          formatter: (cell: any) => {
            const data = cell.getRow().getData();
            const canvas = document.createElement("canvas");
            canvas.width = 20;
            canvas.height = 20;

            requestAnimationFrame(() => {
              this.drawPieChart(
                canvas,
                data.measuresSatisfied,
                data.measuresUnsatisfied,
                "#99d2f8",
                "#0071bc"
              );
            });
            return canvas;
          },
          hozAlign: "center",
          minWidth: 20,
          maxWidth: 30,
        },
        {
          title: "<div style='text-align: right'><div class='svg-icon unsatisfied-icon'></div></div>",
          titleDownload: "Measures Unsatisfied",
          field: "measuresUnsatisfied",
          hozAlign: "right",
          headerSort: false,
          width: 40,
          cssClass: "darkBlue"
        },
        {
          title: "Measures Unsatisfied",
          titleDownload: "%",
          field: "unsatisfiedPercentage",
          hozAlign: "center",
          headerWordWrap: true,
          width: 90,
          cssClass: "darkBlue",
          formatter: (cell: any) => `(${cell.getValue()}%)`
        },
        { title: "New", field: "newMeasures", hozAlign: "center", cssClass: "whiteBorder", width: 50 },
        { title: "Ongoing", field: "ongoingMeasures", hozAlign: "center" },
        { title: "Recently Satisfied", field: "recentlySatisfied", hozAlign: "center", headerWordWrap: true, width: 80 },
        { title: "Score", field: "operaScore", hozAlign: "center", headerHozAlign: "left", width: 80, cssClass: "whiteBorder",
          formatter: (cell: any) => {
            const value = cell.getValue();
            if (value === 'Leader') {
              return `<span style="color: #0071bc;">${value}</span>`;
            } else if (value === 'Median') {
              return `<span style="color: #808080;">${value}</span>`;
            } else {
              return `<span style="color: #0071bc;">${value}</span>`;
            }
          }
         },
        {
          title: "OPERA&copy; Average",
          titleDownload: "OPERA (c) Average %",
          field: "operaAverage",
          hozAlign: "right",
          headerWordWrap: true,
          width: 76,
          formatter: (cell: any) => `${cell.getValue()}%`
        },

      ]

    });

    // Subscribe to the tableBuilt event
    // https://tabulator.info/docs/6.3/events#table
    this.table.on("tableBuilt", () => {
      const columns = this.table.getColumns();
      this.columnWidths = columns.map((col: any) => col.getWidth());
      this.groupByFields = this.table.options.groupBy;

      // Set the group headers after the table is built
      // https://tabulator.info/docs/6.3/group#manage
      this.table.setGroupHeader((value: any, _count: any, data: any[], group: any) => {
        // Calculate the summation of the columns

        const totalMeasures = data.reduce((sum: number, row: any) => sum + row.totalMeasures, 0);
        const measuresSatisfied = data.reduce((sum: number, row: any) => sum + row.measuresSatisfied, 0);
        const measuresUnsatisfied = data.reduce((sum: number, row: any) => sum + row.measuresUnsatisfied, 0);
        const newMeasures = data.reduce((sum: number, row: any) => sum + row.newMeasures, 0);
        const ongoingMeasures = data.reduce((sum: number, row: any) => sum + row.ongoingMeasures, 0);
        const recentlySatisfied = data.reduce((sum: number, row: any) => sum + row.recentlySatisfied, 0);
        const satisfiedPercentage = Math.round((measuresSatisfied / totalMeasures) * 100);
        const unsatisfiedPercentage = Math.round((measuresUnsatisfied / totalMeasures) * 100);
        const operaAverage = Math.round((data.reduce((sum: number, row: any) => sum + row.operaAverage, 0) / data.length) * 100) / 100;

        const groupField = group.getField();
        const groupLevel = this.groupByFields.indexOf(groupField);
        const paddingLevel = 100; //padding on the left side of each column
        let indentationWidth: number = 32 + (groupLevel * paddingLevel);

        // Create the custom group header HTML string
        let groupHeaderHtml = `<div class="tabulator-group-toggle tabulator-row tabulator-unselectable tabulator-calcs tabulator-calcs-top tabulator-row-even" role="row">`;
        groupHeaderHtml += `<div class="tabulator-cell" role="gridcell" style="display: none;">&nbsp;</div>`;
        groupHeaderHtml += `<div class="tabulator-cell" role="gridcell" style="display: none;">&nbsp;</div>`;
        groupHeaderHtml += `<div class="tabulator-cell" role="gridcell" style="display: none;">&nbsp;</div>`;
        groupHeaderHtml += `<div class="tabulator-cell" role="gridcell" style="width: ${this.columnWidths[3] + this.columnWidths[4] - indentationWidth}px;">${value}</div>`;
        groupHeaderHtml += `<div class="tabulator-cell" role="gridcell" style="width: ${this.columnWidths[5]}px;">&nbsp;</div>`;
        groupHeaderHtml += `<div class="tabulator-cell" role="gridcell" style="width: ${this.columnWidths[6]}px; text-align: center;">${totalMeasures}</div>`;
        groupHeaderHtml += `<div class="tabulator-cell lightBlue" role="gridcell" style="width: ${this.columnWidths[7]}px; text-align: right;">${measuresSatisfied}</div>`;
        groupHeaderHtml += `<div class="tabulator-cell lightBlue" role="gridcell" style="width: ${this.columnWidths[8]}px; text-align: center;">(${satisfiedPercentage}%)</div>`;
        groupHeaderHtml += `<div class="tabulator-cell" role="gridcell" style="width: ${this.columnWidths[9]}px; text-align: center;" data-piechart data-satisfied="${measuresSatisfied}" data-unsatisfied="${measuresUnsatisfied}"></div>`;
        groupHeaderHtml += `<div class="tabulator-cell darkBlue" role="gridcell" style="width: ${this.columnWidths[10]}px; text-align: right;">${measuresUnsatisfied}</div>`;
        groupHeaderHtml += `<div class="tabulator-cell darkBlue" role="gridcell" style="width: ${this.columnWidths[11]}px; text-align: center;">(${unsatisfiedPercentage}%)</div>`;
        groupHeaderHtml += `<div class="tabulator-cell" role="gridcell" style="width: ${this.columnWidths[12]}px; text-align: center;">${newMeasures}</div>`;
        groupHeaderHtml += `<div class="tabulator-cell" role="gridcell" style="width: ${this.columnWidths[13]}px; text-align: center;">${ongoingMeasures}</div>`;
        groupHeaderHtml += `<div class="tabulator-cell" role="gridcell" style="width: ${this.columnWidths[14]}px; text-align: center;">${recentlySatisfied}</div>`;
        groupHeaderHtml += `<div class="tabulator-cell" role="gridcell" style="width: ${this.columnWidths[15]}px;">&nbsp;</div>`;
        groupHeaderHtml += `<div class="tabulator-cell" role="gridcell" style="width: ${this.columnWidths[16]}px; text-align: right;">${operaAverage}%</div>`;
        groupHeaderHtml += `</div>`;

        return groupHeaderHtml;

      });

      // Attach scroll synchronization logic
      const tableElement = document.querySelector("#quality-measures-table .tabulator-tableholder");
      if (tableElement) {
        tableElement.addEventListener("scroll", (event) => {
          const scrollLeft = (event.target as HTMLElement).scrollLeft;
          this.syncGroupHeaderScroll(scrollLeft);
        });
      }
    });

    this.table.on("columnResized", () => {
      // Update custom group column widths when columns are resized
      // https://tabulator.info/docs/6.3/events#column
      const columns = this.table.getColumns();
      this.columnWidths = columns.map((col: any) => col.getWidth());
    });

    this.table.on("headerClick", (_e: any, column: any) => {
      // Check if the clicked column is the filter column
      // https://tabulator.info/docs/6.3/callbacks#column
      if (column.getField() === "filter") {
        this.toggleLocationGrouping();
      }
    });

    this.table.on("renderComplete", () => {
      this.renderGroupHeaderPieCharts();
    });

  }

  syncGroupHeaderScroll(scrollLeft: number): void {
    // Find all group headers and synchronize their scroll position
    // with the table's scroll position
    const groupHeaders = document.querySelectorAll(".tabulator-group");
    groupHeaders.forEach((header) => {
      (header as HTMLElement).style.transform = `translateX(-${scrollLeft}px)`;
    });
  }

  renderGroupHeaderPieCharts(): void {
    // Find all elements with the data-piechart attribute
    const pieChartElements = document.querySelectorAll('[data-piechart]');
    pieChartElements.forEach((element) => {
      // Clear any existing content
      element.innerHTML = '';

      const canvas = document.createElement('canvas');
      canvas.width = 20;
      canvas.height = 20;

      const satisfied = parseInt(element.getAttribute('data-satisfied') || '0', 10);
      const unsatisfied = parseInt(element.getAttribute('data-unsatisfied') || '0', 10);

      // Draw the pie chart using Chart.js
      this.drawPieChart(canvas, satisfied, unsatisfied, "#99d2f8", "#0071bc");

      // Append the canvas to the element
      element.appendChild(canvas);
    });
  }

  // Download functions
  // https://tabulator.info/docs/6.3/download
  downloadExcel(): void {
    this.table.download("xlsx", "quality-measures.xlsx");
  }

  downloadPDF(): void {
    this.table.download("pdf", "quality-measures.pdf", {
      orientation: "landscape",
      title: "Quality Measures",
    });
  }

  downloadCSV(): void {
    this.table.download("csv", "quality-measures.csv");
  }

  printTable(): void {
    this.table.print(false, true, true, true, true, true, true, true, true, true, false);
  }

  // Register Chart.js components
  private registerChartComponents(): void {
    Chart.register(PieController, ArcElement, Tooltip, Legend);
  }

  // Create pie chart using Chart.js
  drawPieChart(canvas: HTMLCanvasElement, satisfied: number, unsatisfied: number,
    satisfiedColor: string = '#99d2f8', unsatisfiedColor: string = '#0071bc'): void {

    const total = satisfied + unsatisfied;
    if (total === 0) return;

    // Create Chart.js pie chart
    new Chart(canvas, {
      type: 'pie',
      data: {
        datasets: [{
          data: [unsatisfied, satisfied],
          backgroundColor: [unsatisfiedColor, satisfiedColor],
          borderWidth: 1,
          borderColor: '#99d2f8',
          // Add a slight border radius for a smoother look
          borderRadius: 1
        }]
      },
      options: {
        responsive: false,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            enabled: false
          }
        },
        interaction: {
          mode: undefined, // Disable all interactions
          intersect: false
        },
        animation: false,
        rotation: -90 * Math.PI / 180, // Start from the right side
      }
    });
  }

  // This function toggles the grouping of the table
  toggleLocationGrouping(): void {
    this.toggleGrouping = !this.toggleGrouping;

    if (this.toggleGrouping) {
      this.table.setGroupBy(["qualityName"]);
    } else {
      this.table.setGroupBy(["qualityName", "groupName", "locationName"]);
    }
  }

  /**
   * Calculate the total sum of all totalMeasures in the dataset
   * @returns The sum of all totalMeasures
   */
  getTotalMeasuresSum(): number {
    if (!this.qualityMeasures || this.qualityMeasures.length === 0) {
      return 0;
    }

    return this.qualityMeasures.reduce((sum, measure) => sum + measure.totalMeasures!, 0);
  }

  /**
   * Handle filter changes from the filter component
   */
  onFiltersChanged(filters: QualityMeasureFilters): void {
    this.currentFilters = filters;
    console.log('Filters changed in parent component:', filters);

    // Here you would typically call your API with the new filters
    // For now, we'll just log the filters

    // Example of how you might call your API when it's ready:
    // this.spinnerService.show();
    // this.qualityMeasuresService.getMeasuresWithFilters(filters).subscribe(data => {
    //   this.qualityMeasures = data.map(measure => ({
    //     ...measure,
    //     totalMeasures: measure.measuresSatisfied + measure.measuresUnsatisfied,
    //     satisfiedPercentage: Math.round((measure.measuresSatisfied / (measure.measuresSatisfied + measure.measuresUnsatisfied)) * 100),
    //     unsatisfiedPercentage: Math.round((measure.measuresUnsatisfied / (measure.measuresSatisfied + measure.measuresUnsatisfied)) * 100)
    //   }));
    //   this.table.setData(this.qualityMeasures);
    //   this.spinnerService.hide();
    // });
  }

  /**
   * Handle report selection from the report selector component
   */
  onReportSelected(report: any): void {
    console.log('Report selected in parent component:', report);

    // Here you would typically navigate to the selected report
    // or update the current view based on the selected report

    // Example:
    // if (report.route) {
    //   this.router.navigateByUrl(report.route);
    // }
  }
}

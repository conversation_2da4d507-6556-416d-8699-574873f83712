export interface HuddleDetail {
    demographicsId: number;
    mrn: number; // Helper Function extractMRN()
    providerId?: number | null;
    providerName: string;
    locationId: number;
    locationName: string;
    patientName: string;
    patientFullName: string;
    measureGap?: number | null;
    targetGaps?: string;
    vaccineName: string;
    scheduleDt: Date;
    scheduleTime?: Date | null;
    statusDescription?: string;
    huddleFlags?: string;
    hivFlag: number;
    hcvFlag: number;
    FlowSheetImage: string;
    rowNum: number;
    details: string[];
    showAllDetails: boolean;
    appointmentType: string;
}
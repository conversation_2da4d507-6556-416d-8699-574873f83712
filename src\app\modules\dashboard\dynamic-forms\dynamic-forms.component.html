<div class="titleContainer">
  <div class="leftTitle">
      <span class="formFiller">Form Filler</span>
  </div>
  <div class="centerTitle">
      <h2 class="formName">{{formName}}</h2>
      <p class="description" *ngIf="!containsPlaceholder(WorkFlowDescription)">{{WorkFlowDescription}}</p>
  </div>
</div>

<div *ngIf="!submitted; else submittedResults" class="pageWrapper">

  <!-- Left Column containing Progress Tracker and Info Panel -->
  <div class="leftColumn">

    <div class="progressWrapper">
      <!-- Progress Tracker -->
      <div class="stepper-container" [ngClass]="{'history-mode-stepper': isHistory}">
        <mat-horizontal-stepper [linear]="true" #stepper (selectionChange)="onStepChange($event)" [disableRipple]="true">
          <mat-step [stepControl]="fillFormGroup" [editable]="true">
            <ng-template matStepLabel>
              <span class="step-label">{{ isHistory ? 'Forms' : 'Fill' }}</span>
            </ng-template>
          </mat-step>

          <mat-step [stepControl]="signFormGroup" [editable]="false">
            <ng-template matStepLabel>
              <span class="step-label">{{ isHistory ? 'Review' : 'Sign' }}</span>
            </ng-template>
          </mat-step>

          <mat-step [editable]="false" *ngIf="!isHistory">
            <ng-template matStepLabel>
              <span class="step-label">Submit</span>
            </ng-template>
          </mat-step>
        </mat-horizontal-stepper>
      </div>
    </div>

     <!-- Info Panel (Checklist) -->
    <div class="infoPanel" [ngSwitch]="stepper.selectedIndex">
      <div *ngSwitchCase=0>
        <div class="infoPanelTitle">Required Fields</div>
        <mat-accordion *ngFor="let form of forms; let i=index" multi>
            <mat-expansion-panel [expanded]="formExpandedState[i]">
                <mat-expansion-panel-header class="infoHeaderBg" (click)="toggleSection(i)">
                    <mat-panel-title class="infoTitle">
                        {{ getDictionaryValue(formNames, form.FormName) }}
                        <span class="badge" *ngIf="!allFieldsValid(form) && !isHistory">
                            {{ getRemainingFields(form) }} left
                        </span>
                        <mat-icon *ngIf="allFieldsValid(form) || isHistory" class="checkIcon">check</mat-icon>
                    </mat-panel-title>
                </mat-expansion-panel-header>
                <div *ngFor="let field of form.Fields">
                    <div class="infoItem" *ngIf="field.isRequired">
                        <div class="item itemName-Limiter" (click)="highlightControl(field)"
                              [ngClass]="{'highlighted-checklist-item': isFieldHighlighted(field.FieldName)}">
                            {{ field.Label }}
                        </div>
                        <div class="item" *ngIf="(dynamicForm.controls[field.FieldName].valid &&
                                                  (dynamicForm.controls[field.FieldName].dirty || dynamicForm.controls[field.FieldName].touched) || isHistory); else elseIcon">
                            <mat-icon class="blueText">check</mat-icon>
                        </div>
                        <ng-template #elseIcon>
                            <div class="item right-item">
                                <mat-icon>radio_button_off</mat-icon>
                            </div>
                        </ng-template>
                    </div>
                </div>
            </mat-expansion-panel>
        </mat-accordion>
        </div>
        <div *ngSwitchCase=1>
          <mat-accordion  multi>
            <mat-expansion-panel [expanded]="true">
              <mat-expansion-panel-header class="infoHeaderBg" (click)="toggleSection(0)">
                <mat-panel-title class="infoTitle">Review & Sign Forms</mat-panel-title>
              </mat-expansion-panel-header>
              <div class="infoItem" *ngFor="let pdfs of PdfFillerRules; let i = index">
                <div class="item itemName-Limiter">
                {{pdfs.Name}}
                </div>
                <div class="item" *ngIf="(this.formsSubmitted!=null && this.formsSubmitted[i] !=null && this.formsSubmitted[i].isSigned==true) || this.isHistory; else elseIcon">
                            <mat-icon class="blueText">check</mat-icon>
                </div>
                <ng-template #elseIcon>
                  <div class="item right-item">
                    <mat-icon class="blueText">radio_button_off</mat-icon>
                  </div>
                </ng-template>
              </div>
            </mat-expansion-panel>
          </mat-accordion>
        </div>
        <div *ngSwitchCase=2>
          <div class="submission-status-card">
            <div class="status-header">
              <span class="card-status-icon"></span>
              <!-- If the API call is successful, display the submitted message with the Lead ID -->
              <span class="status-text" *ngIf="apiResultStatus">Submitted - LeadId: {{leadId}}</span>
              <!-- If the API call failed, display a failure message -->
              <span class="status-text" *ngIf="!apiResultStatus">Submission Failed</span>
            </div>
            <div class="status-details">
              <p>Submitted by {{formUserSubmitted}}</p>
              <p>Date submitted {{lastSubmitted}}</p>
              <p>Submitted to Asembia</p>
            </div>
          </div>
        </div>
    </div>

  </div>


  <!-- Form Section -->
  <div class="rightContent" [ngSwitch]="stepper.selectedIndex">
    <div *ngSwitchCase=0>
      <form class="formWrapper" [formGroup]="dynamicForm">
        <mat-accordion *ngFor="let form of forms; let i = index" multi>
          <mat-expansion-panel [expanded]="formExpandedState[i]">
            <mat-expansion-panel-header class="accordionHeader" (click)="toggleSection(i)">
              <mat-panel-title> {{getDictionaryValue(formNames,form.FormName)}} </mat-panel-title>
            </mat-expansion-panel-header>
            <div *ngFor="let row of groupFormFields(form.Fields); trackBy: trackByIndex" class="fieldRow">
              <div *ngFor="let field of row; trackBy: trackByFieldName">
                <div class="fieldWrapper" [ngSwitch]="field.FieldType" [ngClass]="{'highlighted-control': isFieldHighlighted(field.FieldName)}">
                  <div class="fieldWrapper" [ngSwitch]="field.FieldType">

                    <!-- Dropdown -->
                    <div class="field" *ngSwitchCase="'dropdown'">
                      <label>{{field.Label}}</label>
                      <select formControlName="{{field.FieldName}}" [ngClass]="getClass(field.FieldType)"  (change)="onDropdownChange(field, $event)">
                        <option *ngFor="let option of field.Attributes | keyvalue : noSort" [ngValue]="option.value" [disabled]="isHistory" [selected]="option.value">{{option.value}}</option>
                      </select>
                    </div>

                    <!-- Textarea -->
                    <div class="field" *ngSwitchCase="'textarea'">
                      <label>{{field.Label}}</label>
                      <textarea rows="4" formControlName="{{field.FieldName}}" [ngClass]="getClass(field.FieldType)" [readonly]="isHistory"></textarea>
                    </div>

                    <!-- Checkbox -->
                    <div class="field" *ngSwitchCase="'checkbox'">
                      <label>{{field.Label}}</label>
                      <div class="checkboxInput" *ngFor="let option of field.Attributes | keyvalue let i = index" formArrayName="{{field.FieldName}}" class="input-row">
                        <input type="checkbox" [formControlName]="i" [value]="option.value" [disabled]="isHistory" (change)="isHistory ? null : onCheckboxChange(field, option.value, $event)"/>
                        <label class="checkbox-label">{{option.value}}</label>
                      </div>
                    </div>

                    <!-- Radio -->
                    <div class="field" *ngSwitchCase="'radio'">
                      <label>{{field.Label}}</label>
                      <div *ngFor="let option of field.Attributes | keyvalue" class="input-row">
                        <input type="radio" [formControlName]="field.FieldName" [value]="option.value" [disabled]="isHistory" (click)="onRadioClick(field, option.value, $event)" />
                        <label>{{option.value}}</label>
                      </div>
                    </div>

                    <!-- Table -->
                    <div class="field" *ngSwitchCase="'table'">
                      <label>{{ field.Label }}</label>
                      <!-- Wrapper div to control the height and enable scrolling -->
                      <div class="tableWrapper">
                        <table mat-table [dataSource]="tableDataSource[field.FieldName]" class="mat-elevation-z8">

                          <!-- Checkbox Column SelectAll -->
                          <ng-container matColumnDef="select" *ngIf="field.ValidationCondition?.includes('Selectable')">
                            <th mat-header-cell *matHeaderCellDef>
                              <mat-checkbox (change)="$event ? selectAll(field.FieldName, $event.checked) : clearSelection(field.FieldName)" [disabled]="isHistory" [checked]="isAllSelected(field.FieldName)">All</mat-checkbox>
                            </th>
                            <td mat-cell *matCellDef="let row">
                              <mat-checkbox [ngModelOptions]="{standalone: true}" [disabled]="isHistory" [ngModel]="row.select" (change)="onSelectRow(row)"></mat-checkbox>
                            </td>
                          </ng-container>

                          <!-- Define columns dynamically based on the field attributes -->
                          <ng-container *ngFor="let attribute of getTableAttributes(field)" [matColumnDef]="attribute.key">
                            <th mat-header-cell *matHeaderCellDef>{{ attribute.value }}</th>
                            <td mat-cell *matCellDef="let element">{{ element[attribute.key] }}</td>
                          </ng-container>

                          <!-- Header and row definitions -->
                          <tr mat-header-row *matHeaderRowDef="tableColumns[field.FieldName]"></tr>
                          <tr mat-row *matRowDef="let row; columns: tableColumns[field.FieldName];"></tr>
                        </table>
                      </div>
                    </div>

                    <!-- Default Input -->
                    <div class="field" *ngSwitchDefault>
                      <label>{{field.Label}}</label>
                      <input type="{{field.FieldType}}" formControlName="{{field.FieldName}}" [pattern]="pattern" [ngClass]="getClass(field.FieldType)" [readonly]="isHistory || !field.isEditable"/>
                    </div>

                    <!-- Validation Error -->
                    <div *ngIf="field.FieldType && field.FieldType.toLowerCase() != 'table' &&  (!dynamicForm.controls[field.FieldName].valid && (dynamicForm.controls[field.FieldName].dirty || dynamicForm.controls[field.FieldName].touched))">
                      <div class="ValidationError" *ngIf="dynamicForm.controls[field.FieldName].getError('customValidator') as error">
                        {{ error.message }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div>
              <button type="button" class="btn btn-primary btn-sm focusButton" (click)="nextSection(i)"><span class="downArrow">&gt;</span> Next Section</button>
            </div>
          </mat-expansion-panel>
        </mat-accordion>

        <div class="buttonWrapper">
          <button type="submit" class="btn btn-primary btn-sm btnWidth formButton" [disabled]="!dynamicForm.valid && !isHistory" (click)="continueStep()">Continue</button>
        </div>
      </form>
    </div>
     <!-- PDF Viewer (Step 2) -->
     <div *ngSwitchCase="1">
      <ng-container class="pdf-viewer-container">
      <form>
        <mat-accordion *ngFor="let pdfs of PdfFillerRules; let i = index" multi>
          <mat-expansion-panel [expanded]="formExpandedState[i]">
            <mat-expansion-panel-header class="accordionHeader" (click)="toggleSection(i)">
              <mat-panel-title>{{pdfs.Name}}</mat-panel-title>
            </mat-expansion-panel-header>
            <ng-template matExpansionPanelContent>
              <div class="pdf-frame-wrapper">
                <!-- PDF loading indicator -->
                <div *ngIf="isHistory && (!pdfLoadedStatus[i] || pdfDownloading)" class="pdf-loading-indicator">
                  <div class="loading-spinner"></div>
                  <p>Loading PDF document...</p>
                </div>
                <iframe id="pdfFiller_{{i}}" title="PDF Document {{pdfs.Name}}" class="pdf-frame" [src]="pdfFillerUrl[i]" (load)="onPdfIframeLoad(i)" [hidden]="!renderPdfSection[i]"></iframe>
              </div>
              <div class="buttonWrapper">
                <button type="button" class="btn btn-primary btn-sm focusButton" (click)="nextSection(i)"><span class="downArrow">&gt;</span> Next Section</button>
              </div>
            </ng-template>
          </mat-expansion-panel>
        </mat-accordion>

        <div class="buttonWrapper">
          <button type="button" *ngIf="!isHistory" class="btn btn-secondary btn-sm btnWidth formButton" (click)="goBackToFillStep()">Back</button>
          <button type="button" *ngIf="!isHistory" class="btn btn-primary btn-sm btnWidth formButton" [disabled]="!validateAllPdfsAreSigned()" (click)="submitPdfContinue()">Submit</button>
          <button type="button" *ngIf="isHistory" class="btn btn-secondary btn-sm btnWidth formButton" (click)="backToFormsStep()">Back</button>
      </div>
      </form>
      </ng-container>
    </div>
    <div *ngSwitchCase=2>
      <div class="form-status">
        <div class="status-icon">
          <!-- Show a check circle icon if the API call was successful -->
          <mat-icon *ngIf="apiResultStatus" class="check-icon">check_circle</mat-icon>

          <!-- Show a cancel (or X) icon if the API call failed -->
          <mat-icon *ngIf="!apiResultStatus" class="fail-icon">cancel</mat-icon>
        </div>

        <h2 class="status-title">{{formName}}</h2>
        <p class="status-subtitle">Complete</p>

        <div class="button-group">
          <button type="button" mat-button class="view-form-button" (click)="back()">View Form</button>
          <button type="button" mat-raised-button color="primary" class="done-button formButton" (click)="routeToDashboard()">Done</button>
        </div>
      </div>
    </div>
  </div>

</div>

<ng-template #submittedResults>
    <div>results</div>
    <!--
    <div class="infoField" *ngFor="let field of fields">
        <div>
            <label>{{field.FieldName}}</label>
            <div>{{field.SelectedValue}}</div>
        </div>
    </div>
    -->
    <mat-tree [dataSource]="dataSource" [treeControl]="treeControl">
        <!-- Template for expandable nodes -->
        <mat-tree-node *matTreeNodeDef="let node; when: hasChild" matTreeNodePadding>
          <button mat-icon-button matTreeNodeToggle [attr.aria-label]="'toggle ' + node.name">
            <mat-icon>
              {{treeControl.isExpanded(node) ? 'expand_more' : 'chevron_right'}}
            </mat-icon>
          </button>
          {{ forms[node.name] ? (node.name + ': ' + getDictionaryValue(formNames, forms[node.name].FormName)) : node.name }}
        </mat-tree-node>

        <!-- Template for leaf nodes -->
        <mat-tree-node *matTreeNodeDef="let node" matTreeNodePadding>
          {{node.name}}: {{node.value}}
        </mat-tree-node>
      </mat-tree>

    <div>
        <button type="button" (click)="back()">back</button>
    </div>
</ng-template>
